// ==UserScript==
// @name         TEMU TOOLS 2025/06/19/09/00
// @namespace    http://tampermonkey.net/
// @version      2.5
// @description  Send HTTP requests and fetch cookies with a button click
// <AUTHOR>
// @match        *://seller.kuajingmaihuo.com/*
// @match        *://agentseller.temu.com/*
// @connect      eagletemu.jxkhc02.com
// @grant        GM_xmlhttpRequest
// @grant        GM_cookie
// @run-at       document-idle
// ==/UserScript==

(function () {
    function compareDate(targetYear, targetMonth, targetDay) {
        // 设计的年月日时间
        const targetDate = new Date(targetYear, targetMonth - 1, targetDay);  // 月份从0开始，所以减1

        // 当前时间
        const currentDate = new Date();

        // 比较日期（只考虑年月日，不考虑时间）
        return targetDate.setHours(0, 0, 0, 0) <= currentDate.setHours(0, 0, 0, 0);
    }
    createButton();
    function startCountdown(button, seconds) {
        let remaining = seconds;
        const countdownInterval = setInterval(() => {
            if (remaining > 0) {
                button.innerText = `请稍候... (${remaining}s)`;
                remaining--;
            } else {
                clearInterval(countdownInterval);
                button.disabled = false; // 启用按钮
            }
        }, 1000); // 每秒更新一次
    }
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    function create_button(name) {
        const button = document.createElement('button');
        button.innerText = name;
        button.style.padding = '10px';
        button.style.width = '80%'; // 调整按钮宽度
        return button
    }
    function create_input(name) {
        const nameInput = document.createElement('input');
        // 根据输入类型设置不同的input type
        if (name === '选择日期') {
            nameInput.type = 'date';
        } else if (name === '场次选择') {
            // 创建下拉框
            const select = document.createElement('select');
            select.style.width = '86%';
            select.style.marginBottom = '10px';
            select.style.padding = '5px';

            // 添加选项
            const options = ['临近场次', '全部场次'];
            options.forEach(optionText => {
                const option = document.createElement('option');
                option.value = optionText;
                option.textContent = optionText;
                select.appendChild(option);
            });

            return select;
        } else {
            nameInput.type = 'text';
        }
        nameInput.placeholder = name;
        nameInput.style.width = '80%'; // 调整输入框宽度
        nameInput.style.marginBottom = '10px';
        nameInput.style.padding = '5px';
        return nameInput
    }
    function ban_button(button_dict) {
        for (var key_input in button_dict) {
            if (button_dict[key_input] instanceof HTMLElement) { // 确保是 DOM 元素
                button_dict[key_input].disabled = true; // 禁用按钮
            }
        }
    }
    function hidden_input_or_button(yes_or_no, elements) {
        for (var element in elements) {
            if (yes_or_no) {
                elements[element].style.display = 'block';
            } else {
                elements[element].style.display = 'none';
            }
        }
    }
    function select_add_options(buttons) {
        var select = document.createElement('select');
        for (var key_button in buttons) {
            var option1 = document.createElement('option');
            option1.value = key_button;
            option1.textContent = buttons[key_button];
            select.appendChild(option1);
        }
        return select
    }
    function createButton() {
        const VS_Code = '2.5'; // 版本号
        let EnCODE = 'WYM'
        const LUCK = compareDate(2025, 12, 15)
        if (LUCK) {
            return
        } else { EnCODE = 'XX' }
        // 创建输入框和按钮容器
        const container = document.createElement('div');
        container.style.position = 'fixed';
        container.style.top = '100px';
        container.style.right = '10px';
        container.style.zIndex = '9999';
        container.style.backgroundColor = 'white';
        container.style.border = '1px solid black';
        container.style.padding = '10px';
        container.style.boxShadow = '0px 0px 10px rgba(0, 0, 0, 0.1)';
        container.style.width = '250px'; // 调整盒子的宽度
        container.style.transition = 'all 0.3s'; // 添加过渡效果
        if (EnCODE !== 'XX') { return } else { EnCODE = 'GWZZ' }
        // 添加Flexbox样式
        container.style.display = 'flex';
        container.style.flexDirection = 'column';
        container.style.alignItems = 'center'; // 使内容居中对齐
        // 创建收缩按钮
        const toggleButton = document.createElement('button');
        toggleButton.innerText = '收缩';
        toggleButton.style.marginBottom = '10px';
        // 创建一个div容器，用于放置输入框和按钮
        const inputContainer = document.createElement('div');
        inputContainer.style.display = 'flex';
        inputContainer.style.flexDirection = 'column';
        inputContainer.style.width = '100%'; // 使输入容器填满父容器
        inputContainer.style.alignItems = 'center'; // 使内容居中对齐
        const store_input = {
            'cookieInput': '输入Cookie信息',
            'idInput': '店铺ID', 'HDidInput': '活动ID',
            'MSidInput': '秒杀类型活动ID',
            'ThematicIdInput': 'thematicId类型活动ID',
            'activityTypeInput': 'thematicId类型活动类型',
            'dateInput': '选择日期',
            'sessionSelect': '场次选择'
        }
        const store_button = {
            'index': '请选择你需要的操作',
            'button': '自动核价',
            'button1': '平台自动降价',
            // 'button2': '主动降价',
            'button3': '同步产品',
            // 'button4': '优惠券自动配置',
            'button5': '产品下架',
            'button6': '活动同步',
            // 'button7':'国内活动配置',
            'button8': '秒杀类/大促活动配置',
            // 'button9': '编辑合规信息',
            'button10': 'thematicId类活动按钮',
            'button11': '同步财务信息',
            'button12': '暂停运营账号库存调0',
            'button13': '已下架商品库存调0',
            'button14': '泛欧弹窗移除',
            'button15': '同步未发布站点数据',
            'button16': '流量限制中产品调价'
        }
        const new_select = select_add_options(store_button)
        new_select.style.marginTop = '5px';
        let myGlobals_input = {};
        let myGlobals_button = {};
        // 创建版本号显示元素
        const versionDisplay = document.createElement('div');
        versionDisplay.innerText = '版本号: ' + VS_Code;
        versionDisplay.style.marginTop = '10px';
        versionDisplay.style.fontSize = '12px';
        versionDisplay.style.color = 'gray';
        container.appendChild(toggleButton);
        inputContainer.appendChild(new_select);
        for (var key_input in store_input) {
            const new_input = create_input(store_input[key_input])
            const new_create_input = new_input
            myGlobals_input[key_input] = new_create_input;
            container.appendChild(new_create_input);
            new_create_input.style.display = 'none'
        }
        if (EnCODE != 'GWZZ') { return } else { EnCODE = 'ZXHY' }
        for (var key_button in store_button) {
            const new_button = create_button(store_button[key_button])
            const new_create_button = new_button
            myGlobals_button[key_button] = new_create_button;
            container.appendChild(new_create_button);
            new_create_button.style.display = 'none'
        }
        inputContainer.appendChild(versionDisplay); // 添加版本号显示
        // 将收缩按钮和输入容器添加到主容器中
        container.appendChild(inputContainer);
        document.body.appendChild(container);
        if (EnCODE != 'ZXHY') { return } else { EnCODE = 'LZJ' }
        inputContainer.style.display = 'none';
        container.style.width = '50px'; // 调整盒子的最小化宽度
        container.style.height = '50px'; // 调整盒子的最小化高度
        toggleButton.innerText = '展开';
        if (EnCODE != 'LZJ') { inputContainer.style.display = 'none'; container.style.display = 'none'; }
        // 收缩按钮点击事件
        toggleButton.addEventListener('click', function () {
            if (inputContainer.style.display === 'none') {
                inputContainer.style.display = 'flex';
                container.style.width = '250px'; // 恢复原来的宽度
                container.style.height = 'auto'; // 恢复原来的高度
                toggleButton.innerText = '收缩';
            } else {
                inputContainer.style.display = 'none';
                container.style.width = '50px'; // 调整盒子的最小化宽度
                container.style.height = '50px'; // 调整盒子的最小化高度
                toggleButton.innerText = '展开';
                hidden_input_or_button(false, myGlobals_button)
                hidden_input_or_button(false, myGlobals_input)
            }
        });
        // 监听下拉框的变化
        new_select.addEventListener('change', function () {
            // 根据选中的选项显示对应的输入框
            hidden_input_or_button(false, myGlobals_button)
            hidden_input_or_button(false, myGlobals_input)
            if (new_select.value === 'button') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button'].style.display = 'block';
            } else if (new_select.value === 'button1') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button1'].style.display = 'block';
            }
            // else if (new_select.value === 'button2') {
            //     myGlobals_input["cookieInput"].style.display = 'block';
            //     myGlobals_input["idInput"].style.display = 'block';
            //     myGlobals_button['button2'].style.display = 'block';
            // }
            else if (new_select.value === 'button3') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button3'].style.display = 'block';
            }
            // else if (new_select.value === 'button4') {
            //     myGlobals_input["cookieInput"].style.display = 'block';
            //     myGlobals_input["idInput"].style.display = 'block';
            //     myGlobals_button['button4'].style.display = 'block';
            // }
            else if (new_select.value === 'button5') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button5'].style.display = 'block';
            } else if (new_select.value === 'button6') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                // myGlobals_input["dateInput"].style.display = 'block'; // 新增：显示日期输入框
                myGlobals_button['button6'].style.display = 'block';
            } else if (new_select.value === 'button8') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_input["MSidInput"].style.display = 'block';
                myGlobals_input["sessionSelect"].style.display = 'block';
                myGlobals_button['button8'].style.display = 'block';
            }
            // else if (new_select.value === 'button9') {
            //     myGlobals_input["cookieInput"].style.display = 'block';
            //     myGlobals_input["idInput"].style.display = 'block';
            //     myGlobals_button['button9'].style.display = 'block';
            // } 
            else if (new_select.value === 'button10') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_input["ThematicIdInput"].style.display = 'block';
                myGlobals_input["activityTypeInput"].style.display = 'block';
                myGlobals_button['button10'].style.display = 'block';
            } else if (new_select.value === 'button11') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button11'].style.display = 'block';
            } else if (new_select.value === 'button12') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button12'].style.display = 'block';
            } else if (new_select.value === 'button13') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button13'].style.display = 'block';
            }
            else if (new_select.value === 'button14') {
                myGlobals_button['button14'].style.display = 'block';
                myGlobals_button['button14'].disabled = false;
            }
            else if (new_select.value === 'button15') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button15'].style.display = 'block';
            }
            else if (new_select.value === 'button16') {
                myGlobals_input["cookieInput"].style.display = 'block';
                myGlobals_input["idInput"].style.display = 'block';
                myGlobals_button['button16'].style.display = 'block';
            }

        });
        myGlobals_button['button'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button'].innerText = '运行中...';
                try {
                    await fetchData(cookies, inputID, VS_Code);
                    myGlobals_button['button'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie和ID');
            }
        });
        myGlobals_button['button1'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button1'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button1'].innerText = '运行中...';
                try {
                    await PlatformReducePriceLogic(cookies, inputID, VS_Code);
                    myGlobals_button['button1'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button1'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button1'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button1'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button1'], 1000); // 开始倒计时，60秒
                    console.log(error)

                }
            } else {
                alert('请输入Cookie和ID');
            }
        });
        // myGlobals_button['button2'].addEventListener('click', async function () {
        //     const cookies = myGlobals_input["cookieInput"].value;
        //     const inputID = myGlobals_input["idInput"].value;
        //     if (cookies && inputID) {
        //         ban_button(myGlobals_button)
        //         myGlobals_button['button2'].style.backgroundColor = 'orange'; // 运行中颜色
        //         myGlobals_button['button2'].innerText = '运行中...';
        //         try {
        //             await InitiativeReDucePriceMain(cookies, inputID, VS_Code);
        //             myGlobals_button['button2'].style.backgroundColor = 'green'; // 运行结束颜色
        //             startCountdown(myGlobals_button['button2'], 1000); // 开始倒计时，60秒
        //         } catch (error) {
        //             myGlobals_button['button2'].style.backgroundColor = 'red'; // 发生错误时的颜色
        //             myGlobals_button['button2'].innerText = '发生错误';
        //             startCountdown(myGlobals_button['button2'], 1000); // 开始倒计时，60秒
        //             console.log(error)

        //         }
        //     } else {
        //         alert('请输入Cookie和ID');
        //     }
        // });
        myGlobals_button['button3'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button3'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button3'].innerText = '运行中...';
                try {
                    await UpdateProductRequestDetialMain(cookies, inputID, VS_Code);
                    myGlobals_button['button3'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button3'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button3'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button3'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button3'], 1000); // 开始倒计时，60秒
                    console.log(error)

                }
            } else {
                alert('请输入Cookie和ID');
            }
        });
        // myGlobals_button['button4'].addEventListener('click', async function () {
        //     const cookies = myGlobals_input["cookieInput"].value;
        //     const inputID = myGlobals_input["idInput"].value;
        //     if (cookies && inputID) {
        //         ban_button(myGlobals_button)
        //         myGlobals_button['button4'].style.backgroundColor = 'orange'; // 运行中颜色
        //         myGlobals_button['button4'].innerText = '运行中...';
        //         try {
        //             await YouHuiQuanMain(cookies, inputID, VS_Code);
        //             myGlobals_button['button4'].style.backgroundColor = 'green'; // 运行结束颜色
        //             startCountdown(myGlobals_button['button4'], 1000); // 开始倒计时，60秒
        //         } catch (error) {
        //             myGlobals_button['button4'].style.backgroundColor = 'red'; // 发生错误时的颜色
        //             myGlobals_button['button4'].innerText = '发生错误';
        //             startCountdown(myGlobals_button['button4'], 1000); // 开始倒计时，60秒
        //             console.log(error)

        //         }
        //     } else {
        //         alert('请输入Cookie和ID');
        //     }
        // });
        myGlobals_button['button5'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button5'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button5'].innerText = '运行中...';
                try {
                    await FeedbackMain(cookies, inputID, VS_Code);
                    myGlobals_button['button5'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button5'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button5'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button5'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button5'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie和ID');
            }
        });
        myGlobals_button['button6'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            // const submitDate = myGlobals_input["dateInput"]?.value || '';
            // console.log('提交日期', submitDate)
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button6'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button6'].innerText = '运行中...';
                try {
                    await WeekendayProductUPDATEMain(cookies, inputID, VS_Code);
                    // await newWeekendayProductUPDATEMain(cookies, inputID, VS_Code);
                    myGlobals_button['button6'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button6'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button6'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button6'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button6'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie和ID,活动id');
            }
        });
        myGlobals_button['button8'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            const activityType = myGlobals_input["MSidInput"].value;
            const sessionSelect = myGlobals_input["sessionSelect"].value;
            if (cookies && inputID && activityType) {
                ban_button(myGlobals_button)
                myGlobals_button['button8'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button8'].innerText = '运行中...';
                try {
                    //以前的秒杀类活动配置
                    // await MSproductPriceActivityMain(activityType, cookies, inputID, VS_Code);
                    //20250307新的秒杀类、大促活动配置
                    await seckillPromotionMain(activityType, cookies, inputID, sessionSelect, VS_Code);
                    myGlobals_button['button8'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button8'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button8'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button8'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button8'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie和ID,活动秒杀类id');
            }
        });
        // myGlobals_button['button9'].addEventListener('click', async function () {
        //     const cookies = myGlobals_input["cookieInput"].value;
        //     const inputID = myGlobals_input["idInput"].value;
        //     if (cookies && inputID) {
        //         ban_button(myGlobals_button)
        //         myGlobals_button['button9'].style.backgroundColor = 'orange'; // 运行中颜色
        //         myGlobals_button['button9'].innerText = '运行中...';
        //         try {
        //             await HGXXEditMain(cookies, inputID, VS_Code);
        //             myGlobals_button['button9'].style.backgroundColor = 'green'; // 运行结束颜色
        //             startCountdown(myGlobals_button['button9'], 1000); // 开始倒计时，60秒
        //         } catch (error) {
        //             myGlobals_button['button9'].style.backgroundColor = 'red'; // 发生错误时的颜色
        //             myGlobals_button['button9'].innerText = '发生错误';
        //             startCountdown(myGlobals_button['button9'], 1000); // 开始倒计时，60秒
        //             console.log(error)
        //         }
        //     } else {
        //         alert('请输入Cookie和ID');
        //     }
        // });
        myGlobals_button['button10'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            const thematicId = myGlobals_input["ThematicIdInput"].value
            const activityType = myGlobals_input["activityTypeInput"].value
            if (cookies && inputID && thematicId && activityType) {
                ban_button(myGlobals_button)
                myGlobals_button['button10'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button10'].innerText = '运行中...';
                try {
                    await ThematicIdActivateMain(cookies, inputID, thematicId, activityType, VS_Code);
                    myGlobals_button['button10'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button10'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button10'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button10'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button10'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,thematicId,ID');
            }
        });
        myGlobals_button['button11'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button11'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button11'].innerText = '运行中...';
                try {
                    await FinacialMain(cookies, inputID, VS_Code);
                    myGlobals_button['button11'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button11'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button11'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button11'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button11'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,ID');
            }
        });
        myGlobals_button['button12'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button12'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button12'].innerText = '运行中...';
                try {
                    await zeroOutAccountInventory(cookies, inputID, VS_Code);
                    myGlobals_button['button12'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button12'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button12'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button12'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button12'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,ID');
            }
        });
        myGlobals_button['button13'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button13'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button13'].innerText = '运行中...';
                try {
                    await adjustInventoryToZeroForRemovedProducts(cookies, inputID, VS_Code);
                    myGlobals_button['button13'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button13'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button13'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button13'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button13'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,ID');
            }
        });
        myGlobals_button['button14'].addEventListener('click', async function () {
            try {
                myGlobals_button['button14'].disabled = false;
                await fopopupRemoved();
            } catch (error) {
                console.log(error)
            }

        });
        myGlobals_button['button15'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button15'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button15'].innerText = '运行中...';
                try {
                    await syncUnpublishedDataToSite(cookies, inputID, VS_Code);
                    myGlobals_button['button15'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button15'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button15'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button15'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button15'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,ID');
            }
        });
        myGlobals_button['button16'].addEventListener('click', async function () {
            const cookies = myGlobals_input["cookieInput"].value;
            const inputID = myGlobals_input["idInput"].value;
            if (cookies && inputID) {
                ban_button(myGlobals_button)
                myGlobals_button['button16'].style.backgroundColor = 'orange'; // 运行中颜色
                myGlobals_button['button16'].innerText = '运行中...';
                try {
                    await adjustProductPriceDuringTrafficLimit(cookies, inputID, VS_Code);
                    myGlobals_button['button16'].style.backgroundColor = 'green'; // 运行结束颜色
                    startCountdown(myGlobals_button['button16'], 1000); // 开始倒计时，60秒
                } catch (error) {
                    myGlobals_button['button16'].style.backgroundColor = 'red'; // 发生错误时的颜色
                    myGlobals_button['button16'].innerText = '发生错误';
                    startCountdown(myGlobals_button['button16'], 1000); // 开始倒计时，60秒
                    console.log(error)
                }
            } else {
                alert('请输入Cookie,ID');
            }
        });
    }
    //活动库存校验值
    const activeInventoryCheck = 50;
    //欧盟国家映射表
    const euSiteMap = {
        'DE': '德国站',
        'FR': '法国站',
        'IT': '意大利站',
        'ES': '西班牙站',
        'NL': '荷兰站',
        'PL': '波兰站',
        'CZ': '捷克站',
        'SK': '斯洛伐克站',
        'HU': '匈牙利站',
        'RO': '罗马尼亚站',
        'PT': '葡萄牙站',
        'BE': '比利时站',
        'GR': '希腊站',
        'AT': '奥地利站',
        'IE': '爱尔兰站',
        'SE': '瑞典站',
        'DK': '丹麦站',
        'FI': '芬兰站',
        'HR': '克罗地亚站',
        'SI': '斯洛文尼亚站',
        'LT': '立陶宛站',
        'EE': '爱沙尼亚站',
        'LV': '拉脱维亚站',
        'MT': '马耳他站',
        'LU': '卢森堡站',
        'CY': '塞浦路斯站',
        'BG': '保加利亚站'

    };
    function getNameToCode(name) {
        for (const [code, siteName] of Object.entries(euSiteMap)) {
            if (siteName === name) {
                return code; // 找到对应的编码
            }
        }
        return null; // 未找到时返回 null
    }
    // 验证数据有效性
    function isValidData(data) {
        return data?.result?.dataList && Array.isArray(data.result.dataList) && data.result.dataList.length > 0;
    }
    //同步财务信息
    function BalanceListInfo(cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 5000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/api/merchant/payment/account/withdraw/cash/record',
                data: JSON.stringify({
                    'page': 1,
                    'pageSize': 20,
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const dataList1 = data1;
                        resolve(dataList1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('ERP 请求超时');
                }
            });
        });
    }
    function BalanceInfo(cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 50000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/api/merchant/payment/account/amount/info',
                data: JSON.stringify({}),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const dataList1 = data1.result;
                        resolve(dataList1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function SendBalanceInfo(rawbody, accountId, versions) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/updateAccountFund',
                data: JSON.stringify(
                    {
                        "data": {
                            "rawbody": rawbody
                        },
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function SendBalanceListInfo(rawbody, accountId, versions) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/updateAccountWithdraw',
                data: JSON.stringify(
                    {
                        "data": {
                            "rawbody": rawbody
                        },
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    async function FinacialMain(cookies, mallid, VS_Code) {
        console.log('开始同步财务信息操作------------------------')
        try {
            const balanceinfo = await BalanceInfo(cookies, mallid)
            if (balanceinfo) {
                const sendinfo = await SendBalanceInfo(balanceinfo, mallid, VS_Code)
                console.log('BLerp_info', sendinfo)
                if (sendinfo.state == 999) {
                    console.log('请检查软件版本')
                    return
                }
            } else {
                console.log('获取财务信息失败', balanceinfo)
            }
            const sendlistinfo = await BalanceListInfo(cookies, mallid)
            if (sendlistinfo) {
                const erpsendinfo = await SendBalanceListInfo(sendlistinfo, mallid, VS_Code)
                console.log('erplist_info', erpsendinfo)
                if (erpsendinfo.state == 999) {
                    console.log('请检查软件版本')
                    return
                }
            } else {
                console.log('获取财务列表信息失败', sendlistinfo)
            }
        } catch (error) {
            console.log('财务信息获取失败', error)
        }
    }
    //自动核价代码
    function sendRequestPrice(priceOrderId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch/info/query',
                data: JSON.stringify({ 'orderIds': [priceOrderId] }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const dataList1 = data1.result;
                        resolve(dataList1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function sendRequestVerifyPrice(skcId, extCode, price, currency, sitePriceList, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/ProductAtuoPrice',
                data: JSON.stringify(
                    {
                        "data": {
                            "skc": skcId,
                            "skuNo": extCode,
                            "platformPrice": price,
                            "currency": currency,
                            "sitePriceList": sitePriceList,
                        },
                        "accountId": accountId,
                        "versions": versions
                    },
                ),
                headers: {
                    'Content-Type': 'application/json',
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("ERP Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("ERP Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function sendRequestPrice_FO(productId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch/info/query-by-spu',
                data: JSON.stringify({ 'productIdList': [productId] }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const dataList1 = data1.result;
                        resolve(dataList1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function ClickToConfirm(priceOrderId, productSkuId, price, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch',
                data: JSON.stringify(
                    { "itemRequests": [{ "priceOrderId": priceOrderId, "supplierResult": 1, "items": [{ "productSkuId": productSkuId, "price": price }] }] }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const click_verify_data = data1;
                        resolve(click_verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function RefuseClickToConfirm(priceOrderId, productSkuId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                // url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magneto/price/bargain-no-bom/batch',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch',
                data: JSON.stringify(
                    { "itemRequests": [{ "priceOrderId": priceOrderId, "supplierResult": 3, "items": [{ "productSkuId": productSkuId }] }] }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const refuse_click_verify_data = data1;
                        resolve(refuse_click_verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function PricesToConfirm(itemRequests, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch',
                data: JSON.stringify(
                    {
                        "itemRequests": itemRequests
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const refuse_click_verify_data = data1;
                        resolve(refuse_click_verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    async function fetchData(cookies, mallid, VS_Code) {
        console.log('开始自动核价操作------------------------')
        try {
            await FinacialMain(cookies, mallid, VS_Code);
        } catch (error) {
            console.log('同步财务信息异常:', error)
        }

        const additionalParams = {
            supplierTodoTypeList: [1],
            // productSkcIdList: [19189601794]
        };
        const data2 = await getNewProductLifecycleData(1, 20, additionalParams, cookies, mallid);
        if (data2 && data2.result) {
            let pageNum = Math.ceil(data2.result.total / 50)
            console.log('总页数:', pageNum)
            if (pageNum >= 99) { pageNum = 99 }
            if (pageNum) {
                for (let page = pageNum; page > 0; page--) {
                    try {
                        console.log('当前页数:', page)
                        const data = await getNewProductLifecycleData(page, 50, additionalParams, cookies, mallid);
                        // 确保 data 和 data.result 存在
                        if (data && data.result && data.result.dataList) {
                            const dataList = data.result.dataList;
                            // 检查 dataList 是否存在并且是一个数组
                            if (Array.isArray(dataList) && dataList.length > 0) {
                                // 使用 async 函数包裹主循环，以便于使用 await
                                for (let i = 0; i < dataList.length; i++) {
                                    try {
                                        const item = dataList[i];
                                        const priceOrderId = item.skcList[0]?.supplierPriceReviewInfoList[0]?.priceOrderId;
                                        const skcId = item.skcList[0]?.skcId;
                                        const extCode = item.skcList[0]?.skuList[0]?.extCode;
                                        const skuId = item.skcList[0]?.skuList[0]?.skuId;
                                        console.log('处理商品：', extCode, skcId);
                                        // 确保所有必要的属性都存在
                                        if (!priceOrderId || !skcId || !extCode || !skuId) {
                                            console.log('缺少必要的数据，跳过');
                                            continue;
                                        }
                                        await delay(520); // 等待一秒
                                        let extCode_suffix = extCode.includes('_EU_FO')
                                        if (!extCode_suffix) {
                                            try {
                                                const dataList1 = await sendRequestPrice(priceOrderId, cookies, mallid);
                                                const price = dataList1?.priceReviewItemList[0].skuInfoList[0].suggestSupplyPrice;
                                                const suggestPriceCurrency = dataList1?.priceReviewItemList[0].skuInfoList[0].suggestPriceCurrency;
                                                console.log(`skcId: ${skcId}, skuId: ${skuId}, extCode: ${extCode}, price: ${price}, suggestPriceCurrency: ${suggestPriceCurrency}`);
                                                // 确保价格信息存在
                                                if (!price || !suggestPriceCurrency) {
                                                    console.log('未能获取有效的价格信息,跳过当前产品', skcId, extCode, dataList1);
                                                    continue;
                                                }
                                                // 发送验证价格请求
                                                const verify_data = await sendRequestVerifyPrice(skcId, extCode, price / 100, suggestPriceCurrency, null, VS_Code, mallid);
                                                console.log('erp_info:', verify_data);
                                                // 处理验证结果
                                                if (verify_data.state) {
                                                    const verify_code = verify_data.state;

                                                    if (verify_code === 999) {
                                                        alert('插件需要在规定时间内使用，或请及时更新版本');
                                                        return; // 结束函数的执行
                                                    }
                                                    if (verify_code === 2) {
                                                        const click_verify_data = await ClickToConfirm(priceOrderId, skuId, price, cookies, mallid);
                                                        console.log('成功核价信息:', click_verify_data);
                                                    }
                                                    if (verify_code === -1) {
                                                        const click_verify_data = await RefuseClickToConfirm(priceOrderId, skuId, cookies, mallid);
                                                        console.log('拒绝核价信息:', click_verify_data);
                                                    }
                                                } else {
                                                    console.log('未能获取有效的验证信息', verify_data);
                                                }
                                            } catch (error) {
                                                console.error('未获取到有效信息:', error);
                                                continue;
                                            }

                                        } else {
                                            console.log('处理泛欧FO商品:', extCode, skcId);
                                            const productId = item.productId;
                                            const dataListFo = await sendRequestPrice_FO(productId, cookies, mallid);
                                            if (dataListFo.priceReviewItemList.length > 0) {
                                                // 如果确认价格信息不是27个站点，则存在价格申报中的数据，则跳过当前循环
                                                if (dataListFo.priceReviewItemList.length < 27) {
                                                    console.log('当前产品存在价格申报中的站点,跳过', skcId, extCode);
                                                    continue;
                                                }
                                                // 提取 skc 和 skuNo
                                                const skc = dataListFo.priceReviewItemList[0]?.skcId;
                                                const skuNo = dataListFo.priceReviewItemList[0]?.skuInfoList[0]?.productSkuExtCode;

                                                // 构建 skuInfoList
                                                const extractedList = dataListFo.priceReviewItemList.map(item => ({
                                                    platformPrice: item.skuInfoList[0]?.suggestSupplyPrice / 100, // 转为正常价格格式
                                                    currency: item.skuInfoList[0]?.suggestPriceCurrency,
                                                    site: getNameToCode(item.semiHostedBindSiteNameList[0]) // 获取站点代码
                                                }));
                                                // console.log('extractedList', extractedList);
                                                // 发送验证价格请求
                                                const verify_data = await sendRequestVerifyPrice(skc, skuNo, null, null, extractedList, VS_Code, mallid);
                                                console.log('erp_info:', verify_data);

                                                const verify_code = verify_data.state;
                                                if (verify_code === 999) {
                                                    alert('插件需要在规定时间内使用，或请及时更新版本');
                                                    return; // 结束函数的执行
                                                }
                                                if (verify_code === 0) {
                                                    continue;
                                                }
                                                if (verify_code === 2) {
                                                    // 根据 verify_data 中的 siteAcceptList 设置 supplierResult
                                                    const siteAcceptList = verify_data.siteAcceptList;
                                                    const itemRequests = dataListFo.priceReviewItemList.map(item => {
                                                        const siteCode = getNameToCode(item.semiHostedBindSiteNameList[0]);
                                                        const verifyInfo = siteAcceptList.find(info => info.site === siteCode);

                                                        // 根据不同状态处理
                                                        if (!verifyInfo) return null; // 如果没有验证信息，跳过

                                                        let supplierResult;
                                                        if (verifyInfo.state === 2) {
                                                            supplierResult = 1; // 接受
                                                        } else if (verifyInfo.state === -1) {
                                                            supplierResult = 3; // 拒绝
                                                        } else {
                                                            return null; // 其他状态跳过处理
                                                        }

                                                        return {
                                                            priceOrderId: item.id,
                                                            supplierResult: supplierResult,
                                                            items: item.skuInfoList.map(sku => ({
                                                                productSkuId: sku.productSkuId,
                                                                price: sku.suggestSupplyPrice
                                                            }))
                                                        };
                                                    }).filter(Boolean);
                                                    console.log("Agree_itemRequests:", itemRequests);

                                                    if (itemRequests.length > 0) {
                                                        const click_verify_data = await PricesToConfirm(itemRequests, cookies, mallid);
                                                        console.log('成功核价信息:', click_verify_data);
                                                    } else {
                                                        console.log('没有需要处理的价格信息');
                                                    }
                                                }
                                                if (verify_code === -1) {
                                                    const itemRequests = dataListFo.priceReviewItemList.map(item => ({
                                                        priceOrderId: item.id,
                                                        supplierResult: 3, // 设置 supplierResult 为 3（拒绝）
                                                        items: item.skuInfoList.map(sku => ({
                                                            productSkuId: sku.productSkuId,

                                                        }))
                                                    }));
                                                    console.log("Refuse_itemRequests:", itemRequests);
                                                    const click_verify_data = await PricesToConfirm(itemRequests, cookies, mallid);
                                                    console.log('拒绝核价信息:', click_verify_data);
                                                }
                                            } else {
                                                console.log('没有站点价格信息');
                                            }
                                        }
                                    } catch (error) {
                                        console.log('error:', error)
                                    }
                                }
                            } else {
                                console.log("数据列表为空或不是数组");
                            }

                        } else {
                            console.log("Data or data.result is undefined");
                        }
                    } catch (error) {
                        console.log('获取当前页面数据失败', error)
                        continue
                    }
                }
            }

        }
        else {
            console.log('请输入正确数值')
            return
        }
    }
    //平台自动降价代码
    function PlatformReDucePriceRefuse(adjustId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/gmp/bg/magneto/api/price/purchase-adjust/review',
                data: JSON.stringify({ 'adjustId': adjustId, 'result': 2, 'reason': '利润为负，暂不接受降价。', }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("PlatformReDucePriceRefuse Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("PlatformReDucePriceRefuse Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function PlatformReDucePricePromise(adjustId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/purchase-adjust/review',
                data: JSON.stringify({ 'adjustId': adjustId, 'result': 1 }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function PlatformReDucePriceList(pageNo, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price-adjust/page-query',
                data: JSON.stringify({
                    'pageInfo': {
                        'pageSize': 10,
                        'pageNo': pageNo,
                    },
                    'status': 1
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("PlatformReDucePriceList Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("PlatformReDucePriceList Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function PlatformsendRequestReducePrice(skcId, extCode, price, currency, sitePriceList, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 10000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/ProductAtuoMarkDown',
                data: JSON.stringify(
                    {
                        "data": {
                            "skc": skcId,
                            "skuNo": extCode,
                            "platformPrice": price,
                            "currency": currency,
                            "sitePriceList": sitePriceList,
                        },
                        "versions": versions,
                        "accountId": accountId
                    },
                ),
                headers: {
                    'Content-Type': 'application/json',
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("ERP Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    async function PlatformReducePriceLogic(cookies, mallid, VS_Code) {
        console.log('开始平台自动降价操作------------------------')
        const p_res = await PlatformReDucePriceList(1, cookies, mallid)
        console.log(p_res)
        const total = p_res.result.total
        if (total) {
            const pageNum = Math.ceil(total / 10)
            for (let page = pageNum; page > 0; page--) {
                try {
                    await delay(500); // 等待一秒
                    const p_res_page = await PlatformReDucePriceList(page, cookies, mallid)
                    const P_List = p_res_page.result.list;
                    if (Array.isArray(P_List) && P_List.length > 0) {
                        for (let i = 0; i < P_List.length; i++) {
                            try {
                                const item = P_List[i];
                                const adjustId = item.id;
                                const skcId = item.skcId;
                                const extCode = item.skuInfoItemList[0]?.skuExtCode;
                                const p_price = item.newSupplyPrice;
                                const p_suggestPriceCurrency = item.priceCurrency;
                                const p_activityInvitationName = item.activityInvitationName
                                if (!adjustId || !skcId || !extCode || !p_price || !p_suggestPriceCurrency) {
                                    console.error('缺少必要的数据,跳过当前产品');
                                    continue;
                                }
                                await delay(300); // 等待一秒

                                let extCode_suffix = extCode.includes('_EU_FO')
                                const siteCode = getNameToCode(item.siteNameList[0])
                                if (extCode_suffix) {
                                    //处理泛欧
                                    // 构建 skuInfoList
                                    const sitePriceList = [{
                                        platformPrice: p_price / 100, // 转为正常价格格式
                                        currency: p_suggestPriceCurrency,
                                        site: siteCode  // 获取站点代码
                                    }];
                                    if (sitePriceList != null) {
                                        const platform_verify_data = await PlatformsendRequestReducePrice(skcId, extCode, null, null, sitePriceList, VS_Code, mallid);
                                        console.log(platform_verify_data)
                                        const status_platfom = platform_verify_data.state
                                        if (p_activityInvitationName == null) {
                                            if (status_platfom === 999) {
                                                alert('插件需要在规定时间内使用，或请及时更新版本');
                                                return; // 结束函数的执行
                                            }
                                            if (status_platfom == 2) {
                                                const matchedSite = platform_verify_data.siteAcceptList.find(item => item.site === siteCode);
                                                if (matchedSite) {
                                                    const { state } = matchedSite;
                                                    if (state == 2) {
                                                        console.log('接受调价ID:', adjustId)
                                                        const accept_status = await PlatformReDucePricePromise(adjustId, cookies, mallid)
                                                        console.log('接受调价', accept_status)
                                                    }
                                                }
                                            } else {
                                                console.log('未找到匹配的站点:', siteCode);

                                            }
                                        }
                                    }
                                } else {
                                    console.log(extCode, item.priceOrderSn)
                                    const platform_verify_data = await PlatformsendRequestReducePrice(skcId, extCode, p_price / 100, p_suggestPriceCurrency, [], VS_Code, mallid);
                                    console.log(platform_verify_data)
                                    const status_platfom = platform_verify_data.state
                                    if (p_activityInvitationName == null) {
                                        if (status_platfom === 999) {
                                            alert('插件需要在规定时间内使用，或请及时更新版本');
                                            return; // 结束函数的执行
                                        }
                                        if (status_platfom == 2) {
                                            // console.log('接受调价:', adjustId)
                                            const accept_status = await PlatformReDucePricePromise(adjustId, cookies, mallid)
                                            console.log('接受调价', accept_status)
                                        }
                                        if (status_platfom == -1) {
                                            // console.log('拒绝调价:', adjustId)
                                            const refuse_status = await PlatformReDucePriceRefuse(adjustId, cookies, mallid)
                                            console.log('拒绝调价', refuse_status)
                                        }
                                    }
                                }
                            } catch (error) {
                                console.log('当前接口对接超时，尝试跳过', error)
                                continue
                            }
                        }
                    }
                } catch {
                    console.log('跳过当前页面')
                    continue
                }
            }
        } else {
            console.log('未获取到降价数据')
            return
        }
    }
    //主动降价
    function InitiativesendRequestGainPrice(accountId, versions) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/getProductPriceReduction',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function InitiativesendRequestReturnPriceInfo(skc, skuId, currency, price, state, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/updateProductPriceReduction',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "versions": versions,
                        "data": {
                            "skc": skc,
                            "skuId": skuId,
                            "currency": currency,
                            "price": price,
                            "state": state
                        },
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');

                }
            });
        });
    }
    function InitiativeReDucePricePromisegmpProductBatchAdjustPrice(targetSupplyPrice, productId, skuId, oldSupplyPrice, t_currency, o_currency, productSkcId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/priceAdjust/gmpProductBatchAdjustPrice',
                data: JSON.stringify({
                    'adjustReason': 3,
                    'reason': "加快产品动销。",
                    'adjustItems': [
                        {
                            'productSkcId': productSkcId,
                            'skuAdjustList': [
                                {
                                    'targetPriceCurrency': t_currency,
                                    'oldPriceCurrency': o_currency,
                                    'oldSupplyPrice': oldSupplyPrice,
                                    'skuId': skuId,
                                    'targetSupplyPrice': targetSupplyPrice,
                                    'syncPurchasePrice': 1,
                                },
                            ],
                            'productId': productId,
                            'supplierId': mallid,
                        },
                    ]
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function InitiativeReDucePricePromisegmpProductdetial(productId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price-adjust/product-adjust-query',
                data: JSON.stringify(
                    {
                        'items': [
                            {
                                'supplierId': mallid,
                                'productId': productId,
                            },
                        ],
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }

    async function InitiativeReDucePriceMain(cookies, mallid, VS_Code) {
        console.log('开始主动降价操作------------------------')
        const additionalParams = {
            'secondarySelectStatusList': [12,],
            'supplierTodoTypeList': [],
        };
        const p_res = await getNewProductLifecycleData(1, 10, additionalParams, cookies, mallid)
        const total = p_res.result.total
        console.log(334, total)
        if (total) {
            const i_info_res = await InitiativesendRequestGainPrice(mallid, VS_Code)
            if (i_info_res.state == 999) {
                console.log('请注意版本号和使用时间限制')
                retutn
            }
            const i_info_list = i_info_res.temuProduct
            if (Array.isArray(i_info_list) && i_info_list.length > 0) {
                for (let i = 0; i < i_info_list.length; i++) {
                    try {
                        const item = i_info_list[i];
                        const targetPriceCurrency = item.targetPriceCurrency;
                        const oldPriceCurrency = item.oldPriceCurrency;
                        const skuId = item.skuId;
                        const productSkcId = item.productSkcId;
                        console.log('productSkcId', productSkcId)
                        const targetSupplyPrice = item.targetSupplyPrice
                        const additionalParams2 = {
                            'supplierTodoTypeList': [],
                            'productSkcIdList': [productSkcId,],
                        };
                        const NewproductInfo = await getNewProductLifecycleData(1, 10, productSkcId, additionalParams2, cookies, mallid);
                        const NewProductID = NewproductInfo.result.dataList[0].productId
                        console.log('NewProductID', NewProductID)
                        const NewPriceInfo = await InitiativeReDucePricePromisegmpProductdetial(NewProductID, cookies, mallid)
                        const skcInfos = Object.values(NewPriceInfo.result.spuAdjustResult)
                        const newstatus = NewPriceInfo.success
                        await delay(500); // 等待一秒
                        if (!NewPriceInfo || skcInfos.length < 1) {
                            console.log('获取产品信息失败');
                            continue;
                        }
                        const skcItems = skcInfos[0].skcItems[0].items[0]
                        const priceCurrency = skcItems.priceCurrency
                        const originSupplyPrice = skcItems.originSupplyPrice
                        if (priceCurrency >= targetSupplyPrice * 100) {
                            const update_stauts3 = await InitiativesendRequestReturnPriceInfo(productSkcId, skuId, priceCurrency, targetSupplyPrice, 2, VS_Code, mallid)
                            if (update_stauts3.state == 999) {
                                console.log('请检查版本号和时间使用范围')
                                return
                            }
                            console.log('币种，金额有误')
                            continue
                        }
                        if (!mallid || !productSkcId || !NewProductID || !targetPriceCurrency || !skuId || !targetSupplyPrice) {
                            console.log('缺少必要的数据，跳过当前循环');
                            continue;
                        }
                        const BatchAjust_status = await InitiativeReDucePricePromisegmpProductBatchAdjustPrice(targetSupplyPrice * 100, NewProductID, skuId, originSupplyPrice, targetPriceCurrency, priceCurrency, productSkcId, cookies, mallid)
                        if (BatchAjust_status.success) {
                            const update_stauts1 = await InitiativesendRequestReturnPriceInfo(productSkcId, skuId, priceCurrency, targetSupplyPrice, 1, VS_Code, mallid)
                            if (update_stauts1.state == 999) {
                                console.log('请检查版本号和使用时间范围')
                                return
                            }
                            console.log('上传成功')
                        } else {
                            const update_stauts2 = await InitiativesendRequestReturnPriceInfo(productSkcId, skuId, priceCurrency, targetSupplyPrice, 2, VS_Code, mallid)
                            if (update_stauts2.state == 999) {
                                console.log('请检查版本号和使用时间范围')
                                return
                            }
                            console.log('上传失败')
                        }
                    } catch (e) {
                        console.log('服务异常,请耐心等待1.6秒重试')
                        await delay(1600); // 等待一秒
                        continue
                    }
                }
            } else {
                console.log("店铺数据为空")
            }
        } else {
            console.log('接口需要处理的数据为空')
        }
    }
    //同步产品
    function generateTimestamps() {
        const startDate = new Date('2024-05-01T00:00:00'); // 起始日期，精确到0点0分0秒
        const timeInterval = 10 * 24 * 60 * 60 * 1000; // 10天的毫秒数
        const timestamps = [];
        let currentStartDate = startDate.getTime();
        // 循环生成时间戳对
        while (true) {
            let currentEndDate = currentStartDate + timeInterval - 1;
            let nextStartDate = currentEndDate + 1;

            // 将结束时间设置为当天的23:59:59.999
            let endDate = new Date(currentEndDate);
            endDate.setHours(23, 59, 59, 999); // 设置到当天的23:59:59.999

            // 如果下一个开始时间超过今天，则使用当天时间
            if (nextStartDate > Date.now()) {
                let finalEndDate = new Date(Date.now());
                finalEndDate.setHours(23, 59, 59, 999); // 设置为当前日期的23:59:59.999

                timestamps.push({
                    timeBegin: currentStartDate,
                    timeEnd: finalEndDate.getTime() // 最后一段结束时间为当天23:59:59.999
                });
                break;
            }
            // 添加正常的时间戳对
            timestamps.push({
                timeBegin: currentStartDate,
                timeEnd: endDate.getTime()
            });
            // 更新下一个时间段的开始时间
            currentStartDate = nextStartDate;
        }
        return timestamps;
    }
    function generateTimestampsrandom() {
        const startDate = new Date('2024-05-01T00:00:00'); // 起始日期
        const timestamps = [];
        let currentStartDate = startDate.getTime();

        while (true) {
            // 生成随机天数：6 ~ 19 天
            const randomDays = Math.floor(Math.random() * (19 - 6 + 1)) + 6;
            const timeInterval = randomDays * 24 * 60 * 60 * 1000; // 转换为毫秒

            let currentEndDate = currentStartDate + timeInterval - 1;
            let endDate = new Date(currentEndDate);
            endDate.setHours(23, 59, 59, 999); // 设置为当天的 23:59:59.999

            let nextStartDate = currentEndDate + 1;

            // 如果下一个开始时间超过今天，则使用当前时间作为结束点
            if (nextStartDate > Date.now()) {
                let finalEndDate = new Date(Date.now());
                finalEndDate.setHours(23, 59, 59, 999);

                timestamps.push({
                    timeBegin: currentStartDate,
                    timeEnd: finalEndDate.getTime()
                });
                break;
            }

            // 添加正常的时间段
            timestamps.push({
                timeBegin: currentStartDate,
                timeEnd: endDate.getTime()
            });

            // 更新下一个时间段的起始时间
            currentStartDate = nextStartDate;
        }

        return timestamps;
    }
    function generateMonthlyTimestamps() {
        //按月拆分为时间段
        const startDate = new Date('2024-05-01T00:00:00'); // 起始日期，精确到0点0分0秒
        const timestamps = [];
        let currentDate = new Date(startDate);

        while (true) {
            // 获取当前月份的第一天
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth(); // 0-based
            const firstDayOfMonth = new Date(year, month, 1);
            const timeBegin = firstDayOfMonth.getTime();

            // 获取当前月份的最后一天，并设置时间为 23:59:59.999
            const lastDayOfMonth = new Date(year, month + 1, 0, 23, 59, 59, 999);
            const now = Date.now();

            // 如果当前月份已经超过了今天，则只取到今天为止
            let timeEnd;
            if (lastDayOfMonth.getTime() > now) {
                const finalEndDate = new Date(now);
                finalEndDate.setHours(23, 59, 59, 999);
                timeEnd = finalEndDate.getTime();
                timestamps.push({ timeBegin, timeEnd });
                break;
            } else {
                timeEnd = lastDayOfMonth.getTime();
                timestamps.push({ timeBegin, timeEnd });
            }

            // 进入下一个月
            currentDate.setMonth(currentDate.getMonth() + 1);

            // 如果下个月已经超过当前时间，则停止循环
            if (currentDate.getTime() > now) {
                break;
            }
        }

        return timestamps;
    }
    function UpdateProductRequestDetial(rawBody, accountId, versions) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/SaveTemuOnLineProductInfo',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify({
                    "data": rawBody,
                    "accountId": accountId,
                    "versions": versions
                }),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("ERP Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("ERPRequest Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('ERP请求超时');
                }
            });
        });
    }
    function UpdateProductPromisegmpList(pageNum, pageSize, siteId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier',
                data: JSON.stringify(
                    {
                        'pageSize': pageSize,
                        'pageNum': pageNum,
                        'timeType': 6,
                        'secondarySelectStatusList': [12],
                        'supplierTodoTypeList': [],
                        'siteIdList': [siteId]
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function UpdateProductTimestampList(pageNum, pageSize, siteId, timeBegin, timeEnd, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier',
                data: JSON.stringify(
                    {
                        'pageSize': pageSize,
                        'pageNum': pageNum,
                        'timeBegin': timeBegin,
                        'timeEnd': timeEnd,
                        'timeType': 6,
                        'secondarySelectStatusList': [12],
                        'supplierTodoTypeList': [],
                        'siteIdList': [siteId]
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    async function UpdateProductRequestDetialMain2(cookies, mallid, VS_Code) {
        console.log('开始同步产品操作------------------------')
        const siteList = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 118, 120, 137, 138, 142, 187,]
        const timestampList = generateTimestamps()
        for (const siteID of siteList) {
            try {
                const p_res = await UpdateProductPromisegmpList(1, 20, siteID, cookies, mallid)
                const total = p_res.result?.total;
                console.log(`当前站点: ${siteID}, 总数: ${total}`);
                if (total) {
                    for (let timeindex = timestampList.length - 1; timeindex >= 0; timeindex--) {
                        const timeItem = timestampList[timeindex]
                        if (timeItem) {
                            const timeBegin = timeItem.timeBegin
                            const timeEnd = timeItem.timeEnd
                            const rspList = await UpdateProductTimestampList(1, 20, siteID, timeBegin, timeEnd, cookies, mallid)
                            const TTtotal = rspList.result.total
                            if (TTtotal) {
                                let pageNum = Math.min(Math.ceil(TTtotal / 20), 99);//最大99页
                                console.log(`当前站点: ${siteID}, 时间段: ${new Date(timeBegin).toLocaleDateString()} - ${new Date(timeEnd).toLocaleDateString()}, 总页数: ${pageNum}`);
                                for (let page = pageNum; page > 0; page--) {
                                    try {
                                        await delay(1600); // 等待一秒
                                        const rspListdump = await UpdateProductTimestampList(page, 20, siteID, timeBegin, timeEnd, cookies, mallid)
                                        if (rspListdump.success) {
                                            console.log('成功翻页', page)
                                            const upstatus = await UpdateProductRequestDetial(rspListdump, mallid, VS_Code)
                                            console.log('erp_info', upstatus)
                                            if (upstatus.state == 999) {
                                                console.log('请检查版本，时间状态')
                                                return
                                            }
                                        } else {
                                            console.log('翻页失败', rspListdump)
                                            return;
                                        }
                                    } catch (error) {
                                        console.log('超时跳过:', error)
                                        continue
                                    }
                                }
                            } else {
                                console.log('当前时间段无数据')
                            }
                        }
                    }
                } else {
                    console.log('该站点需要处理的数据为空', siteID)
                }
            } catch (error) {
                console.error(`站点 ${siteID} 处理出错`, error);
                continue;
            }


        }
    }
    async function fetchDataWithRetry(page, limit, payload, cookies, mallid, maxRetries = 3) {
        let retryCount = 0;

        while (retryCount <= maxRetries) {
            const rspListdump = await getNewProductLifecycleData_xhr(page, limit, payload, cookies, mallid);
            // console.log('result:', rspListdump);

            if (rspListdump?.result?.dataList) {
                return rspListdump; // 成功获取数据，返回结果
            } else {
                console.warn(`dataList 不存在或为空，正在进行第 ${retryCount + 1} 次重试...`);
                retryCount++;

                if (retryCount > maxRetries) {
                    console.warn('已达到最大重试次数，放弃请求');
                    return null; // 返回 null 表示失败
                }

                // 可选：延迟 1 秒再重试
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }
    async function UpdateProductRequestDetialMain(cookies, mallid, VS_Code) {
        console.log('开始同步产品操作------------------------')
        const timestampList = generateTimestampsrandom()
        try {
            for (const timeItem of timestampList.reverse()) {
                if (!timeItem) continue;
                const timeBegin = timeItem.timeBegin
                const timeEnd = timeItem.timeEnd
                const p_payload = {
                    'timeBegin': timeBegin,
                    'timeEnd': timeEnd,
                    'timeType': 6,
                    'secondarySelectStatusList': [12],
                    'supplierTodoTypeList': [],
                }
                console.log(`当前时间段: ${new Date(timeBegin).toLocaleDateString()} - ${new Date(timeEnd).toLocaleDateString()}`);
                const rspList = await getNewProductLifecycleData_xhr(1, 50, p_payload, cookies, mallid)
                const TTtotal = rspList?.result?.total;
                if (TTtotal) {
                    let pageNum = Math.min(Math.ceil(TTtotal / 50), 99);//最大99页
                    console.log(`当前时间段总页数: ${pageNum}`);
                    for (let page = pageNum; page > 0; page--) {
                        try {
                            console.log(`当前时间段第${page}页-------------------------`);
                            await delay(1000); // 等待一秒
                            // const rspListdump = await getNewProductLifecycleData_xhr(page, 50, p_payload, cookies, mallid)
                            const rspListdump = await fetchDataWithRetry(page, 50, p_payload, cookies, mallid);
                            console.log('result:', rspListdump)
                            if (!rspListdump?.result?.dataList) {
                                console.warn('rspListdump.result.dataList 不存在或为空');
                            }
                            if (rspListdump.result?.dataList?.length > 0) {
                                rspListdump.result.dataList = rspListdump.result.dataList.filter(item => {
                                    const extCode = item.skcList[0]?.skuList[0]?.extCode;
                                    let extCode_suffix = !extCode.includes('_EU_FO'); // 筛选出不包含 '_EU_FO' 的项
                                    return extCode_suffix;
                                });
                            }
                            // console.log('result.dataList:', rspListdump.result.dataList)
                            if (rspListdump.result?.dataList?.length > 0) {
                                // console.log('成功翻页', page)
                                const upstatus = await UpdateProductRequestDetial(rspListdump, mallid, VS_Code)
                                console.log('erp_info', upstatus)
                                if (upstatus.state == 999) {
                                    console.log('请检查版本，时间状态')
                                    return
                                }
                            }
                        } catch (error) {
                            console.log('处理错误:', error)
                            continue;
                        }
                    }
                } else {
                    console.log('当前时间段无数据')
                }

            }
            console.log('同步产品操作完成')
        } catch (error) {
            console.error('处理错误', error);
        }
    }
    //优惠券
    function splitArray(array, size) {
        const result = [];
        for (let i = 0; i < array.length; i += size) {
            // 使用 slice 方法切分数组，最后一组可以包含剩余的所有元素
            result.push(array.slice(i, i + size));
        }
        return result;
    }
    function YouhuiquanGetAgreementID(cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/coupon/agree/info',
                data: JSON.stringify({}),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function YouhuiquanPutRequestsInfoCreate(title, agreementId, goodsCoupons, beginTime, endTime, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/coupon/batch/create',
                data: JSON.stringify({
                    'title': title,
                    'beginTime': beginTime,
                    'endTime': endTime,
                    'goodsCoupons': goodsCoupons,
                    'agreementId': agreementId,
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function YouhuiquanGainItems(pageNo, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/coupon/page/query',
                data: JSON.stringify({
                    'pageNo': pageNo,
                    'pageSize': 20,
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function YouhuiQuanProductGain(accountId, versions) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/getProductCouponReduction',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "accountId": accountId,
                        "versions": versions,
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function YouhuiquanInfoReturn(rawBody, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/updateProductCouponReduction',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "accountId": accountId,
                        "versions": versions,
                        "data": rawBody
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    async function YouHuiQuanMain(cookies, mallid, VS_Code) {
        console.log('开始优惠券自动配置操作------------------------')
        // 明天的时间戳
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0); // 设置为明天的开始
        const tomorrowTimestamp = tomorrow.getTime();
        // 第七天的时间戳
        const seventhDay = new Date();
        seventhDay.setDate(seventhDay.getDate() + 7);
        seventhDay.setHours(23, 59, 59, 999); // 设置为第七天的结束
        const seventhDayTimestamp = seventhDay.getTime();
        const UserSetYHQItems = await YouhuiQuanProductGain(mallid, VS_Code)
        if (Array.isArray(UserSetYHQItems.temuProduct) && UserSetYHQItems.temuProduct.length > 0) {
            const temuProducts = UserSetYHQItems.temuProduct
            const splitData = splitArray(temuProducts, 20);
            for (let i = 0; i < splitData.length; i++) {
                const Y_item = splitData[i]
                // 获取当前时间
                await delay(1654)
                const now = new Date();
                // 获取时间戳
                const timestampStringTitle = now.getTime().toString() + "YHQ";
                const agreementIditem = await YouhuiquanGetAgreementID(cookies, mallid)
                const agreementId = agreementIditem.result.agreementId
                if (agreementId) {
                    const createResult = await YouhuiquanPutRequestsInfoCreate(timestampStringTitle, agreementId, Y_item, tomorrowTimestamp, seventhDayTimestamp, cookies, mallid)
                    console.log(543, createResult)

                }
            }
            const StaticiTEMUS = await YouhuiquanGainItems(1, cookies, mallid)
            const Y_TOTAL = StaticiTEMUS.result.total
            if (Y_TOTAL) {
                const pageNum = Math.ceil(Y_TOTAL / 20)
                for (let S_i = 0; S_i < pageNum; S_i++) {
                    await delay(1239)
                    try {
                        const rawBody = await YouhuiquanGainItems(S_i, cookies, mallid)
                        console.log(S_i)
                        const ER_P_res = await YouhuiquanInfoReturn(rawBody, VS_Code, mallid)
                        if (ER_P_res.state == 999) {
                            console.log('请检查版本号和使用时间范围')
                            return
                        }
                    } catch (error) {
                        console.log(error)
                        continue
                    }
                }
            }

        } else {
            alert('该账号没有优惠券数据，请在规定时间，版本内使用')
        }
    }
    function WeekendayVerifyPrice(invitationId, skc, skuNo, platformPrice, currency, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/ProductProfitCalculate',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "data": {
                            "skc": skc,
                            "skuNo": skuNo,
                            "platformPrice": platformPrice,
                            "currency": currency,
                            "invitationId": invitationId,
                        },
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function WeekendayVerifyReturnSave(rawData, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/updateProductActivity',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify({
                    "versions": versions,
                    "accountId": accountId,
                    "data": rawData
                }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function WeekendayProductPriceActivity(pageNo, pageSize, cookies, mallid) {
        // 获取当前日期
        const now = new Date();
        // 获取当天开始时间戳（00:00:00）
        // const sessionStartTimeFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).getTime();
        // 获取当天开始时间戳（00:00:00），并减去一天
        const sessionStartTimeFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0).getTime();
        // 获取当天结束时间戳（23:59:59.999）
        const sessionEndTimeTo = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999).getTime();
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/session/list`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify({
                    'pageNo': pageNo,
                    'pageSize': pageSize,
                    // 'sessionEndTimeTo': sessionEndTimeTo,
                    // 'sessionStartTimeFrom': sessionStartTimeFrom
                }),
                headers: {
                    'Content-Type': 'application/json',
                    // "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function WeekendayProductPriceActivityZT(pageNo, pageSize, cookies, mallid) {
        // 获取当前日期
        const now = new Date();
        // 获取当天开始时间戳（00:00:00）
        // const sessionStartTimeFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).getTime();
        // 获取当天开始时间戳（00:00:00），并减去一天
        const sessionStartTimeFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0).getTime();
        // 获取当天结束时间戳（23:59:59.999）
        const sessionEndTimeTo = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999).getTime();
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/activity/auto/apply/shareStockListSemi`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify({
                    'pageNo': pageNo,
                    'pageSize': pageSize,
                    // 'sessionEndTimeTo': sessionEndTimeTo,
                    // 'sessionStartTimeFrom': sessionStartTimeFrom
                }),
                headers: {
                    'Content-Type': 'application/json',
                    // "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    //活动同步
    async function WeekendayProductUPDATEMain(cookies, mallid, VS_Code) {
        console.log('开始活动同步操作------------------------')
        // // 获取当天的时间范围
        const today = new Date();
        // today.setDate(today.getDate() - 1); // 往前减一天
        // 开始时间为当天往前减一天的00:00:00
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 1).getTime();
        //结束时间为当天的23:59:59
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).getTime();

        console.log('开始同步长期活动------------------------');
        const wkitems13 = await WeekendayProductPriceActivity(1, 10, cookies, mallid)
        const total_13 = wkitems13.result.total
        if (total_13) {
            // const pageNum13 = Math.ceil(total_13 / 30)
            const pageNum13 = Math.min(Math.ceil(total_13 / 30), 99)
            for (let p_13 = 1; p_13 <= pageNum13; p_13++) {
                await delay(1010)
                try {
                    let wkitems_p13 = null;
                    wkitems_p13 = await WeekendayProductPriceActivity(p_13, 30, cookies, mallid)
                    if (!wkitems_p13.success && wkitems_p13.errorCode === 2000000) {
                        console.log('接口频率限制: ' + wkitems_p13.errorMsg);
                        await delay(180000)
                        wkitems_p13 = await WeekendayProductPriceActivity(p_13, 30, cookies, mallid)
                    }
                    // 筛选当天的数据
                    // wkitems_p13.result.list = wkitems_p13.result.list.filter(item =>
                    //     item.enrollTime >= startOfDay && item.enrollTime <= endOfDay
                    // );
                    if (wkitems_p13?.result?.list && wkitems_p13.result.list.length > 0) {
                        wkitems_p13.result.list = wkitems_p13.result.list.filter(item => {

                            // 条件1: enrollTime 在当天的范围内
                            const inToday = item.enrollTime >= startOfDay && item.enrollTime <= endOfDay;

                            // 条件2: assignSessionList 是一个非空数组
                            const hasAssignSessions = Array.isArray(item.assignSessionList) && item.assignSessionList.length > 0;

                            return inToday && hasAssignSessions;
                        });
                    }
                    console.log('wkitems_p13:', wkitems_p13)
                    // 处理筛选后的数据
                    if (wkitems_p13.result.list.length > 0) {
                        const storeResult = await WeekendayVerifyReturnSave(wkitems_p13, VS_Code, mallid);
                        console.log(`长期活动第 ${p_13} 页ERP返回结果:`, storeResult);

                        if (storeResult?.state === 999) {
                            console.error(`版本号或使用时间范围验证失败`);
                            return;
                        }
                    }
                } catch (error) {
                    console.log(`长期活动第 ${p_13} 页数据处理出错，错误信息：${error}`);
                    await delay(2001);
                    continue
                }
            }
        } else {
            console.log('长期活动数据为空');
        }
        console.log('开始同步自动报名活动------------------------');
        const wkitems14 = await WeekendayProductPriceActivityZT(1, 10, cookies, mallid)
        const total_14 = wkitems14.result.total
        if (total_14) {
            const pageNum14 = Math.ceil(total_14 / 30)
            for (let p_14 = 1; p_14 <= pageNum14; p_14++) {
                try {
                    const wkitems_p14 = await WeekendayProductPriceActivityZT(p_14, 30, cookies, mallid)
                    // 筛选当天的数据
                    if (wkitems_p14.result.productList.length > 0) {
                        wkitems_p14.result.productList = wkitems_p14.result.productList.filter(item =>
                            item.signUpTime >= startOfDay && item.signUpTime <= endOfDay
                        );
                        console.log('wkitems_p14:', wkitems_p14)
                    }
                    // 处理筛选后的数据
                    if (wkitems_p14.result.productList.length > 0) {
                        const storeResult = await WeekendayVerifyReturnSave(wkitems_p14, VS_Code, mallid);
                        console.log(`自动报名活动第 ${p_14} 页ERP返回结果:`, storeResult);

                        if (storeResult?.state === 999) {
                            console.error(`版本号或使用时间范围验证失败`);
                            return;
                        }
                    } else {
                        console.log(`自动报名活动第 ${p_14} 页没有符合条件的数据，结束当前活动处理`);
                        break;
                    }
                } catch (error) {
                    console.log(`自动报名活动第 ${p_14} 页数据处理出错，错误信息：${error}`);
                    await delay(2001)
                    continue
                }
            }
        } else {
            console.log('自动报名活动数据为空')
        }
    }
    async function WeekendayProductUPDATEMain_old(cookies, mallid, VS_Code) {
        console.log('开始活动同步操作------------------------')
        const wkitems13 = await WeekendayProductPriceActivity(1, 10, cookies, mallid)
        const total_13 = wkitems13.result.total
        if (total_13) {
            const pageNum13 = Math.ceil(total_13 / 30)
            for (let p_13 = 1; p_13 <= pageNum13; p_13++) {
                await delay(1010)
                try {
                    const wkitems_p13 = await WeekendayProductPriceActivity(p_13, 30, cookies, mallid)
                    console.log('wkitems_p13:', wkitems_p13)
                    const p_13_store = await WeekendayVerifyReturnSave(wkitems_p13, VS_Code, mallid)
                    console.log('erp_info13:', p_13_store)
                    if (p_13_store.state == 999) {
                        console.log('请检查版本号和使用时间范围')
                        return
                    }
                    console.log(p_13)
                } catch (error) {
                    await delay(2121);
                    continue
                }
            }
        }
        const wkitems14 = await WeekendayProductPriceActivityZT(1, 10, cookies, mallid)
        const total_14 = wkitems14.result.total
        if (total_14) {
            const pageNum14 = Math.ceil(total_14 / 30)
            for (let p_14 = 1; p_14 <= pageNum14; p_14++) {
                try {
                    const wkitems_p14 = await WeekendayProductPriceActivityZT(p_14, 30, cookies, mallid)
                    console.log('wkitems_p14:', wkitems_p14)
                    const p_14_store = await WeekendayVerifyReturnSave(wkitems_p14, VS_Code, mallid)
                    console.log('erp_info14:', p_14_store)
                    if (p_14_store.state) {
                        console.log('请检查版本号和时间使用范围')
                        return
                    }
                    console.log(p_14)
                } catch (error) {
                    await delay(2001)
                    continue
                }
            }
        }
    }
    //SPU ID搜索,活动同步
    async function WeekendayProductUPDATESPU(cookies, mallid, VS_Code, spu_ids) {
        console.log('开始活动同步操作------------------------')
        // // 获取当天的时间范围
        const today = new Date();
        // today.setDate(today.getDate() - 1); // 往前减一天
        // 开始时间为当天往前减一天的00:00:00
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 1).getTime();
        //结束时间为当天的23:59:59
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999).getTime();

        const longTermActivity = await longTermActivityRegistrationRecord(1, 10, spu_ids, cookies, mallid)
        if (longTermActivity.result.list.length > 0 && longTermActivity.result.total > 0) {
            const pageNumlong = Math.ceil(longTermActivity.result.total / 30)
            for (let page = 1; page <= pageNumlong; page++) {
                await delay(1010)
                try {
                    const wkitemsLong = await longTermActivityRegistrationRecord(page, 30, spu_ids, cookies, mallid)
                    // 筛选当天的数据
                    wkitemsLong.result.list = wkitemsLong.result.list.filter(item =>
                        item.enrollTime >= startOfDay && item.enrollTime <= endOfDay
                    );
                    console.log('wkitemsLong:', wkitemsLong)
                    const erpSyncResult = await WeekendayVerifyReturnSave(wkitemsLong, VS_Code, mallid)
                    console.log('ERP_wkitemsLong:', erpSyncResult)
                    if (erpSyncResult.state == 999) {
                        console.log('请检查版本号和使用时间范围')
                        return
                    }
                } catch (error) {
                    await delay(2121);
                    continue
                }
            }
        }

        const specialTopicActivity = await specialTopicActivitySignupRecord(1, 10, spu_ids, cookies, mallid)
        if (specialTopicActivity.result.list.length > 0 && specialTopicActivity.result.total > 0) {
            const pageNumspecial = Math.ceil(specialTopicActivity.result.total / 30)
            for (let page = 1; page <= pageNumspecial; page++) {
                await delay(1010)
                try {
                    const wkitemsSpecial = await specialTopicActivitySignupRecord(page, 30, spu_ids, cookies, mallid)
                    // 筛选当天的数据
                    wkitemsSpecial.result.list = wkitemsSpecial.result.list.filter(item =>
                        item.enrollTime >= startOfDay && item.enrollTime <= endOfDay
                    );
                    console.log('wkitemsSpecial:', wkitemsSpecial)
                    const erpwkitemsSpecial = await WeekendayVerifyReturnSave(wkitemsSpecial, VS_Code, mallid)
                    console.log('ERP_wkitemsSpecial:', erpwkitemsSpecial)
                    if (erpwkitemsSpecial.state == 999) {
                        console.log('请检查版本号和使用时间范围')
                        return
                    }
                } catch (error) {
                    await delay(2121);
                    continue
                }
            }
        }

    }
    function longTermActivityRegistrationRecord(pageNo, pageSize, productIds, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/enroll/list',
                data: JSON.stringify({
                    'pageNo': pageNo,
                    'pageSize': pageSize,
                    'productIds': productIds
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function specialTopicActivitySignupRecord(pageNo, pageSize, productIds, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/activity/product/applied/list',
                data: JSON.stringify({
                    'pageNo': pageNo,
                    'pageSize': pageSize,
                    'productIds': productIds
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }


    //thematicId类活动
    function ThematicIdProductPriceActivity(activityThematicId, activityType, searchScrollContext, cookies, mallid) {
        let new_json = {
            'searchScrollContext': searchScrollContext,
            'rowCount': 50,
            'activityType': activityType,
            'activityThematicId': activityThematicId,
            'addSite': true,
        }
        if (!searchScrollContext) {
            new_json = {
                'rowCount': 50,
                'activityType': activityType,
                'activityThematicId': activityThematicId,
                'addSite': true,
            }
        }
        //获取当前域名
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/semi/scroll/match`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: new_url,
                data: JSON.stringify(new_json),
                headers: {
                    'Content-Type': 'application/json',
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }

    function ThematicIdProductPriceActivity_fetch(activityThematicId, activityType, searchScrollContext, cookies, mallid) {
        let new_json = {
            'searchScrollContext': searchScrollContext,
            'rowCount': 50,
            'activityType': activityType,
            'activityThematicId': activityThematicId,
            'addSite': true,
        }
        if (!searchScrollContext) {
            new_json = {
                'rowCount': 50,
                'activityType': activityType,
                'activityThematicId': activityThematicId,
                'addSite': true,
            }
        }
        // 获取当前域名
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/semi/scroll/match`
        
        // 使用fetch API替代XMLHttpRequest
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000);

        return fetch(new_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'mallid': mallid
            },
            body: JSON.stringify(new_json),
            signal: controller.signal
        })
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error('match请求失败,HTTP状态码:' + response.status);
            }
            return response.json();
        })
        .catch(error => {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('match请求超时');
            }
            if (error.message.includes('Network')) {
                throw new Error('match网络请求失败');
            }
            throw new Error('match解析响应数据失败：' + error.message);
        });
    }
    function ThematicIdProductSessionlist(activityThematicId, activityType, productIds, mallid) {
        //获取当前域名
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/session/list`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: new_url,
                data: JSON.stringify({
                    'activityType': activityType,
                    'activityThematicId': activityThematicId,
                    'productIds': productIds
                }),
                headers: {
                    'Content-Type': 'application/json',
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("sessionlist Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("sessionlist Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function ThematicIdProductSubmitApply(activityThematicId, activityType, productList, cookies, mallid) {
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/semi/submit`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify({
                    'activityType': activityType,
                    'activityThematicId': activityThematicId,
                    'productList': productList
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    // 辅助函数用于记录提交结果
    function logSubmissionResults(data, result) {
        console.log('活动批量提交数据:', data);
        console.log(`w_result提交数据条数: ${data.length}`);
        console.log('活动批量提交结果:', result);
    }
    async function ThematicIdActivateMain(cookies, mallid, thematicId, activityType, VS_Code) {
        console.log('开始thematicId类活动操作------------------------')
        const domain = getCurrentDomain();
         if (domain == null) {
            console.log('请先跳转到活动页面')
            alert('请先跳转到活动页面')
            return
        }
        const wkitems = await ThematicIdProductPriceActivity(thematicId, activityType, '', cookies, mallid)
        let searchScrollContext = wkitems?.result?.searchScrollContext
        if (searchScrollContext === null || searchScrollContext === undefined) {
            console.warn('searchScrollContext 不存在或为空，终止操作');
            return;
        }
        if (!wkitems.result.hasMore) return;
        let w_result = []
        while (true) {
            try {
                await delay(2000);
                const new_wkitems = await ThematicIdProductPriceActivity(thematicId, activityType, searchScrollContext, cookies, mallid);
                const continue_scroll = new_wkitems.result.hasMore;
                searchScrollContext = new_wkitems.result.searchScrollContext;

                console.log(`continue_scroll: ${continue_scroll}`);

                if (!continue_scroll) {
                    console.log('翻页完毕');
                    if (w_result.length > 0) {
                        const p_wk_data = await ThematicIdProductSubmitApply(thematicId, activityType, w_result, cookies, mallid);
                        logSubmissionResults(w_result, p_wk_data);
                        w_result.length = 0;
                    }
                    return;
                }
                const new_productList = new_wkitems.result.matchList;
                if (Array.isArray(new_productList) && new_productList.length > 0) {
                    for (const w_items of new_productList) {
                        if (!w_items) continue;
                        console.log('------------------------------------------------')
                        try {
                            const salesStock = w_items.salesStock;
                            const productId = w_items.productId;
                            const skcInfo = w_items.activitySiteInfoList[0]?.skcList[0];

                            if (!skcInfo) continue;

                            const skcId = skcInfo.skcId;
                            const skuInfo = skcInfo.skuList[0];

                            if (!skuInfo) continue;

                            const skuId = skuInfo.skuId;

                            if (salesStock < activeInventoryCheck) {
                                console.log(`productId: ${productId}, skcId: ${skcId}, skuId: ${skuId},销售库存:${salesStock}, 库存小于50, 跳过`);
                                continue;
                            }

                            const targetActivityPrice = skuInfo.suggestActivityPrice;
                            const extCode = skuInfo.extCode;
                            const currency = skuInfo.currency;
                            const targetActivityStock = w_items.targetActivityStock;
                            const siteId = w_items.sites[0]?.siteId;

                            if (!productId || !skcId || !skuId || !targetActivityPrice || !extCode || !currency || !targetActivityStock || !siteId) {
                                continue;
                            }

                            // 检查extCode,20250320增加泛欧判断，跳过处理
                            if (extCode.includes('_EU_FO')) {
                                console.log('FO跳过处理');
                                continue;
                            }

                            const erp_result = await WeekendayVerifyPrice(thematicId, skcId, extCode, targetActivityPrice / 100, currency, VS_Code, mallid);
                            console.log(`erp_info:`, erp_result);

                            if (erp_result.state === 999) {
                                console.log('请检查版本，时间状态,请检查账号是否参加活动');
                                return;
                            }

                            if (erp_result.state === 2 && targetActivityStock > 0) {
                                const productIds = [productId];
                                await delay(1000);

                                const session_result = await ThematicIdProductSessionlist(thematicId, activityType, productIds, mallid);

                                if (session_result.result.list.length > 0) {
                                    const sessionId = session_result.result.list.find(item => item.siteId === siteId)?.sessionId;

                                    if (sessionId) {
                                        const productList = {
                                            "productId": productId,
                                            "activityStock": targetActivityStock,
                                            "siteInfoList": [
                                                {
                                                    "siteId": siteId,
                                                    "skcList": [
                                                        {
                                                            "skcId": skcId,
                                                            "skuList": [
                                                                {
                                                                    "skuId": skuId,
                                                                    "activityPrice": targetActivityPrice
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ],
                                            "sessionIds": [sessionId]
                                        };
                                        w_result.push(productList);
                                    }
                                }
                            }
                        } catch (error) {
                            console.log('当前产品处理失败，跳过', error);
                            continue;
                        }
                    }

                    if (w_result.length >= 30) {
                        const p_wk_data = await ThematicIdProductSubmitApply(thematicId, activityType, w_result, cookies, mallid);
                        logSubmissionResults(w_result, p_wk_data);
                        w_result.length = 0;
                    }
                }
            } catch (error) {
                console.log('处理当前页报错:', error);
                continue;
            }
        }

    }



    //秒杀类型活动
    function MSProductPriceActivity(pageNo, pageSize, activityType, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit/marketing/enroll/product/list',
                data: JSON.stringify(
                    {
                        'pageNo': pageNo,
                        'pageSize': pageSize,
                        'activityType': activityType
                    }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function MSProductActivitySiteList(productIds, activityType, cookies, mallid) {
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/session/list`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify(
                    { "productIds": [productIds], "activityType": activityType }
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    function MSProductActivityVerifyPrice(activityType, productList, cookies, mallid) {
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/semi/submit`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify({
                    "activityType": parseInt(activityType),
                    "productList": productList
                }),
                headers: {
                    'Content-Type': 'application/json',
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    async function MSproductPriceActivityMain(activityType, cookies, mallid, VS_Code) {
        console.log('开始秒杀类活动配置操作------------------------')
        const ms_page_info = await MSProductPriceActivity(1, 20, activityType, cookies, mallid)
        const ms_total = ms_page_info.result.total
        if (ms_total) {
            const pageNum = Math.ceil(ms_total / 20)
            for (let page = 1; page <= pageNum; page++) {
                const ms_p = await MSProductPriceActivity(page, 20, activityType, cookies, mallid)
                const ms_list = ms_p.result.list
                if (Array.isArray(ms_list) && ms_list.length > 0) {
                    for (let i = 0; i <= ms_list.length; i++) {
                        const ms_items = ms_list[i]
                        const productId = ms_items.productId
                        const skcId = ms_items.skcList[0].skcId
                        const skuId = ms_items.skcList[0].skuList[0].skuId
                        const suggestActivityPrice = ms_items.skcList[0].skuList[0].sitePriceList[0].suggestActivityPrice
                        const extCode = ms_items.skcList[0].skuList[0].extCode
                        const currency = ms_items.skcList[0].skuList[0].currency
                        const targetActivityStock = ms_items.targetActivityStock
                        const siteId = ms_items.sites[0].siteId
                        console.log(productId, skcId, skuId, suggestActivityPrice, extCode, currency, targetActivityStock)
                        await delay(1810)
                        if (!productId | !skcId | !skuId | !suggestActivityPrice | !extCode | !currency | !targetActivityStock) {
                            continue
                        }
                        const erp_result = await WeekendayVerifyPrice(activityType, skcId, extCode, suggestActivityPrice / 100, currency, VS_Code, mallid)
                        if (erp_result.state == 999) {
                            console.log('请检查版本，时间状态')
                            return
                        }
                        try {
                            if (erp_result.state == 2 && targetActivityStock > 0) {
                                const ms_site_r = await MSProductActivitySiteList(productId, activityType, cookies, mallid)
                                const ms_site_list = ms_site_r.result.list
                                if (Array.isArray(ms_site_list) && ms_site_list.length > 0) {
                                    for (let j = 0; j <= ms_site_list.length; j++) {
                                        await delay(1010)
                                        const new_j = ms_site_list[j]
                                        const productList = [{ "productId": productId, "activityStock": targetActivityStock, "skcList": [{ "skcId": skcId, "skuList": [{ "skuId": skuId, "siteActivityPriceList": [{ "activityPrice": suggestActivityPrice, "siteId": siteId }] }] }], "sessionIds": [new_j.sessionId] }]
                                        const submit_result = await MSProductActivityVerifyPrice(activityType, productList, cookies, mallid)
                                        console.log('提交成功', submit_result)
                                        break
                                    }
                                }
                            }
                        } catch (error) {
                            console.log(error)
                            continue;
                        }
                    }
                }
            }
        }



    }
    //编辑合规信息
    function HGINFOERPport(spuid, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/GetGPSRInfo',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "data": {
                            "spuid": spuid
                        },
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function HGINFOERPstatusupdate(spuid, versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/UpdateGPSRStatus',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "data": {
                            "spuid": spuid
                        },
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function HGINFOGetList(spu_id, page_num, page_size, cookies, mallid) {
        return new Promise((resolve, reject) => {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://agentseller.temu.com/ms/bg-flux-ms/compliance_property/page_query', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('mallid', mallid);
            xhr.timeout = 10000;
            // 如果需要的话，设置 cookie
            //if (cookies) {
            //  xhr.setRequestHeader('cookie', cookies);
            // }
            // 请求完成后的处理
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {  // 请求完成
                    if (xhr.status === 200) {  // 成功
                        try {
                            const data = JSON.parse(xhr.responseText);  // 解析返回的 JSON 数据
                            resolve(data);  // 成功时返回数据
                        } catch (e) {
                            reject('Error parsing JSON: ' + e);  // 解析失败时返回错误
                        }
                    } else {
                        reject('Request failed with status: ' + xhr.status);  // 请求失败时返回错误
                    }
                }
            };
            // 发送请求
            xhr.send(JSON.stringify({
                "spu_id": spu_id,
                'page_num': page_num,
                'page_size': page_size,
                'type': 2,
                'task_status_list': [2]
            }));
            // 超时处理
            xhr.ontimeout = function () {
                reject('Request timed out after ' + timeout + 'ms');  // 超时后拒绝
            };
        });
    }
    function HGINFOeditdetial(goods_id, spu_id, wait_task_list, cookies, mallid) {
        return new Promise((resolve, reject) => {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://agentseller.temu.com/ms/bg-flux-ms/compliance_property/query_detail', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('mallid', mallid);
            xhr.timeout = 10000;

            // 如果需要的话，设置 cookie
            //if (cookies) {
            //  xhr.setRequestHeader('cookie', cookies);
            // }

            // 请求完成后的处理
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {  // 请求完成
                    if (xhr.status === 200) {  // 成功
                        try {
                            const data = JSON.parse(xhr.responseText);  // 解析返回的 JSON 数据
                            resolve(data);  // 成功时返回数据
                        } catch (e) {
                            reject('Error parsing JSON: ' + e);  // 解析失败时返回错误
                        }
                    } else {
                        reject('Request failed with status: ' + xhr.status);  // 请求失败时返回错误
                    }
                }
            };

            // 发送请求
            xhr.send(JSON.stringify(
                {
                    'goods_id': goods_id,
                    'spu_id': spu_id,
                    'wait_task_list': wait_task_list
                }
            ));
            // 超时处理
            xhr.ontimeout = function () {
                reject('Request timed out after ' + timeout + 'ms');  // 超时后拒绝
            };
        });
    }
    function HGINFOVerify(cat_id, spu_id, goods_id, template_edit_request_list, cookies, mallid) {
        return new Promise((resolve, reject) => {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://agentseller.temu.com/ms/bg-flux-ms/compliance_property/edit_compliance', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('mallid', mallid);
            xhr.timeout = 10000;

            // 如果需要的话，设置 cookie
            //if (cookies) {
            //  xhr.setRequestHeader('cookie', cookies);
            // }

            // 请求完成后的处理
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {  // 请求完成
                    if (xhr.status === 200) {  // 成功
                        try {
                            const data = JSON.parse(xhr.responseText);  // 解析返回的 JSON 数据
                            resolve(data);  // 成功时返回数据
                        } catch (e) {
                            reject('Error parsing JSON: ' + e);  // 解析失败时返回错误
                        }
                    } else {
                        reject('Request failed with status: ' + xhr.status);  // 请求失败时返回错误
                    }
                }
            };

            // 发送请求
            xhr.send(JSON.stringify(
                {
                    'cat_id': cat_id,
                    'spu_id': spu_id,
                    'goods_id': goods_id,
                    'template_edit_request_list': template_edit_request_list,
                }
            ));
            xhr.ontimeout = function () {
                reject('Request timed out after ' + timeout + 'ms');  // 超时后拒绝
            };
        });
    }
    async function HGXXEditMain(cookies, mallid, VS_Code) {
        console.log('开始编辑合规信息操作------------------------')
        const HGrep = await HGINFOERPport(mallid, VS_Code, mallid)
        console.log('erp info :', HGrep)
        if (HGrep.State == 999) { return }
        if (HGrep.State == 0) { return }
        if (HGrep.State == 2) {
            const HGErpList = HGrep.dataList
            if (Array.isArray(HGErpList) && HGErpList.length > 0) {
                for (let i = 0; i < HGErpList.length; i++) {
                    await delay(123)
                    try {
                        const HGitem = HGErpList[i]
                        const HG_wkitems = await HGINFOGetList(HGitem.SPUID, 1, 10, cookies, mallid)
                        const HG_wkitem = HG_wkitems.result.data
                        if (Array.isArray(HG_wkitem) && HG_wkitem.length > 0) {
                            const HG_items = HG_wkitem[0]
                            const spu_id = HG_items.spu_id
                            const cat_id = HG_items.cat_id
                            const goods_id = HG_items.goods_id
                            let wait_task_dtolist = HG_items.wait_task_list
                            if (wait_task_dtolist == undefined) {
                                wait_task_dtolist = HG_items.wait_task_dtolist
                            }
                            const HG_editdetial = await HGINFOeditdetial(goods_id, spu_id, wait_task_dtolist, cookies, mallid)
                            const edit_template_list = HG_editdetial.result.template_list
                            const ERP_code = HGitem.BatchNumber
                            if (!ERP_code | !edit_template_list | !wait_task_dtolist) {
                                console.log("数据为空")
                                continue
                            }
                            let template_edit_request_list = wait_task_dtolist
                            let new_template_edit_request_list = []
                            let new_template_dict = {}
                            for (let tem_page = 0; tem_page < edit_template_list.length; tem_page++) {
                                const item_type = edit_template_list[tem_page]
                                let t_type = item_type.task_type
                                if (t_type == undefined) { continue }
                                if (t_type == 6) { new_template_dict[6] = item_type.template_id }
                                if (t_type == 8) { new_template_dict[8] = item_type.template_id }
                                if (t_type == 23) { new_template_dict[23] = item_type.template_id }
                                if (t_type == 31) { new_template_dict[31] = item_type.template_id }
                                if (t_type == 42) { new_template_dict[42] = item_type.template_id }
                                if (t_type == 61) { new_template_dict[61] = item_type.template_id }
                                if (t_type == 67) { new_template_dict[67] = item_type.template_id }
                                if (t_type == 69) { new_template_dict[69] = item_type.template_id }
                                if (t_type == 25) {
                                    new_template_dict[25] = item_type.template_id
                                    new_template_dict["rep_id_25"] = item_type.rep_detail_list[0].rep_id
                                    new_template_dict["rep_name_25"] = item_type.rep_detail_list[0].rep_name
                                }
                                if (t_type == 60) {
                                    new_template_dict[60] = item_type.template_id
                                    new_template_dict["rep_id_60"] = item_type.rep_detail_list[0].rep_id
                                    new_template_dict["rep_name_60"] = item_type.rep_detail_list[0].rep_name
                                }
                            }
                            for (let page = 0; page < template_edit_request_list.length; page++) {
                                const e_item = template_edit_request_list[page]
                                const e_type = e_item.task_type
                                if (e_type == undefined) { continue }
                                if (e_type == 6) {
                                    e_item['template_id'] = new_template_dict[6]
                                    e_item['properties'] = { 1132: [28317], 1561: [36824], 1000100082: [1000130368], 1000100083: [1000130368], 1000100085: [1000130368], 1000100007: [1000100032] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 8) {
                                    e_item['template_id'] = new_template_dict[8]
                                    e_item['properties'] = { "1000100025": [1000130013] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 23) {
                                    e_item['template_id'] = new_template_dict[23]
                                    e_item['properties'] = { "1000100038": [1000130061] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 31) {
                                    e_item['template_id'] = new_template_dict[31]
                                    e_item['properties'] = { "1100100102": [1000140143] }
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 42) {
                                    e_item['template_id'] = new_template_dict[42]
                                    e_item['properties'] = { "1000100110": [1000131288] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 61) {
                                    e_item['template_id'] = new_template_dict[61]
                                    e_item['properties'] = {}
                                    e_item['images'] = {}
                                    e_item['input_text'] = { "1100100115": { "multi_line_inputs": [{ "name": ERP_code }] } }
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 69) {
                                    e_item['template_id'] = new_template_dict[69]
                                    e_item['properties'] = { "1100100128": [1000147443] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 67) {
                                    e_item['template_id'] = new_template_dict[67]
                                    e_item['properties'] = { "1100100123": [1000145743] }
                                    e_item['images'] = {}
                                    e_item['input_text'] = {}
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 25) {
                                    e_item['rep_detail_list'] = [{ "rep_id": new_template_dict.rep_id_25, "rep_name": new_template_dict.rep_name_25 }]
                                    new_template_edit_request_list.push(e_item)
                                }
                                if (e_type == 60) {
                                    e_item['rep_detail_list'] = [{ "rep_id": new_template_dict.rep_id_60, "rep_name": new_template_dict.rep_name_60 }]
                                    new_template_edit_request_list.push(e_item)
                                }

                            }
                            console.log('提交信息:', spu_id)
                            const HG_result = await HGINFOVerify(cat_id, spu_id, goods_id, new_template_edit_request_list, cookies, mallid)
                            console.log('上传反馈信息:', HG_result)
                            if (HG_result.success) {
                                const status_type = await HGINFOERPstatusupdate(spu_id, VS_Code, mallid)
                                console.log('状态已更新', status_type)
                            }
                        } else {
                            if (HGitem.SPUID) {
                                console.log('该spuid产品合规信息已经提交成功', HG_wkitems)
                                const status_type = await HGINFOERPstatusupdate(HGitem.SPUID, VS_Code, mallid)
                                console.log('已经重复提交过，状态已更新', status_type)
                            } else {
                                console.log('erp 数据存在错误')
                            }
                        }
                    } catch (error) {
                        console.log('参数处理异常', error)
                        continue
                    }
                }
            }

        }

    }
    //信息反馈,产品下架
    function ERPInfoFeedback(versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/getProductRemoved',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "data": {},
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function FeedbackInfoSubmit(productSkcId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/ultraman/FeedbackMmsExecuteRpcService/submitFeedback',
                data: JSON.stringify({
                    'feedbackType': 1,
                    'productSkcId': productSkcId,
                    'feedbackProblemType': 27,
                    'remark': '产品停售',
                }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    async function FeedbackMain(cookies, mallid, VS_Code) {
        console.log('开始产品下架操作------------------------')
        try {
            const FeedbackInfoList = await ERPInfoFeedback(VS_Code, mallid)
            console.log('erp_info:', FeedbackInfoList)
            const temuProduct = FeedbackInfoList.temuProduct
            if (temuProduct) {
                for (let i = 0; i < temuProduct.length; i++) {
                    try {
                        const f_item = temuProduct[i]
                        await delay(1234)
                        const skc = f_item.skc
                        if (!skc) {
                            continue
                        }
                        const sub_info = await FeedbackInfoSubmit(skc, cookies, mallid)
                        console.log('成功提交反馈信息:', sub_info, skc)
                    } catch (error) {
                        console.log('提交反馈异常', error)
                        continue
                    }

                }
            } else {
                console.log('需要反馈的信息为空')
            }
        } catch (e) {
            console.log('erp反馈信息获取失败')
            console.error(e)
        }
    }

    //检查账号是否暂停运营
    function checkAccountOperationalStatus(versions, accountId) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 60000,
                method: 'POST',
                url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/CheckAccountStatsu',
                headers: {
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(
                    {
                        "versions": versions,
                        "accountId": accountId
                    }
                ),
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        const verify_data = data1;
                        resolve(verify_data); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('ERP 请求超时');
                }
            });
        });
    }
    function getCommodityList(pageSize, cookies, mallid, page, additionalParams = {}) {
        return new Promise((resolve, reject) => {
            // 基础请求参数
            const baseParams = {
                'page': page,
                'pageSize': pageSize,
                'jitStockQuantitySection': {
                    "leftValue": 1,
                    "rightValue": 100000
                }
            };

            // 合并基础参数和额外参数
            const requestParams = { ...baseParams, ...additionalParams };
            console.log(requestParams);

            const xhr = new XMLHttpRequest();
            xhr.timeout = 50000; // 设置超时为50秒

            xhr.open('POST', 'https://seller.kuajingmaihuo.com/bg-visage-mms/product/skc/pageQuery', true);

            // 设置请求头
            xhr.setRequestHeader('Content-Type', 'application/json');
            // xhr.setRequestHeader('Cookie', cookies);
            xhr.setRequestHeader('mallid', mallid);

            xhr.onload = function () {
                try {
                    if (xhr.status !== 200) {
                        throw new Error(`请求失败，状态码：${xhr.status}`);
                    }
                    const responseData = JSON.parse(xhr.responseText);
                    resolve(responseData);
                } catch (e) {
                    console.error("数据解析错误:", e.message);
                    reject(e);
                }
            };

            xhr.onerror = function (error) {
                console.error("网络请求错误:", error);
                reject(new Error('网络请求失败'));
            };

            xhr.ontimeout = function () {
                reject(new Error('请求超时'));
            };

            // 发送请求
            xhr.send(JSON.stringify(requestParams));
        });
    }
    // 获取全部商品列表
    function OutAccountCommoditylist(pageSize, cookies, mallid, page) {
        return getCommodityList(pageSize, cookies, mallid, page);
    }

    //获取已下架商品列表
    function OutproductCommoditylist(pageSize, cookies, mallid, page) {
        const additionalParams = {
            "skcTopStatus": 200,
            "skcSecondaryStatus": 1
        };
        return getCommodityList(pageSize, cookies, mallid, page, additionalParams);
    }

    // 获取已终止商品列表
    function OutTerminatedProductList(pageSize, cookies, mallid, page) {
        const additionalParams = {
            "skcTopStatus": 200,
            "skcSecondaryStatus": 2
        };
        return getCommodityList(pageSize, cookies, mallid, page, additionalParams);
    }
    //修改库存API
    function updateMmsBtgProductSalesStock(payload, cookies, mallid) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.timeout = 50000; // 设置超时为50秒

            xhr.open('POST', 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/starlaod/btg/sales/stock/updateMmsBtgProductSalesStock', true);

            // 设置请求头
            xhr.setRequestHeader('Content-Type', 'application/json');
            // xhr.setRequestHeader('Cookie', cookies);
            xhr.setRequestHeader('mallid', mallid);

            xhr.onload = function () {
                try {
                    if (xhr.status !== 200) {
                        throw new Error(`请求失败，状态码：${xhr.status}`);
                    }
                    const responseData = JSON.parse(xhr.responseText);
                    resolve(responseData);
                } catch (e) {
                    console.error('数据处理错误:', e.message);
                    reject(e);
                }
            };

            xhr.onerror = function (error) {
                console.error('网络请求错误:', error);
                reject(new Error('网络请求失败'));
            };

            xhr.ontimeout = function () {
                reject(new Error('请求超时'));
            };

            xhr.send(JSON.stringify(payload));
        });
    }
    function queryBtgProductStockInfo(productId, productSkuId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.timeout = 30000; // 设置超时为 30 秒
            xhr.open('POST', 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/starlaod/btg/sales/stock/queryBtgProductStockInfo', true);
            // 设置请求头
            xhr.setRequestHeader('Content-Type', 'application/json');
            // xhr.setRequestHeader('Cookie', cookies);
            xhr.setRequestHeader('mallid', mallid);

            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        resolve(data);
                    } catch (e) {
                        reject(new Error('解析响应数据失败：' + e.message));
                    }
                } else {
                    reject(new Error('请求失败,HTTP状态码:' + xhr.status));
                }
            };

            xhr.onerror = function () {
                reject(new Error('网络请求失败'));
            };

            xhr.ontimeout = function () {
                reject(new Error('请求超时'));
            };

            try {
                xhr.send(JSON.stringify({
                    "productId": productId,
                    "productSkuIdList": productSkuId
                }));
            } catch (e) {
                reject(new Error('发送请求失败：' + e.message));
            }
        });
    }

    async function zeroOutAccountInventory(cookies, mallid, VS_Code) {
        try {
            const erp_res = await checkAccountOperationalStatus(VS_Code, mallid);
            console.log('erp_info:', erp_res);
            const verify_code = erp_res.state;
            // 根据ERP返回判断账号状态
            if (verify_code == 999) {
                console.log('请检查版本号和使用时间范围')
                return
            }
            if (verify_code == 2) {
                //账号已暂停运营，开始库存调0
                // await adjustInventoryToZero(cookies, mallid, OutAccountCommoditylist, '暂停运营账号库存调0');
                await newadjustInventoryToZero(cookies, mallid, OutAccountCommoditylist, '暂停运营账号库存调0');
            }
        } catch (e) {
            console.log('账号状态检查失败！');
            console.error(e);
        }
    }

    async function adjustInventoryToZeroForRemovedProducts(cookies, mallid, VS_Code) {

        try {
            // 处理已下架商品
            await adjustInventoryToZero(cookies, mallid, OutproductCommoditylist, '已下架商品库存调0');

            // 处理已终止商品
            await adjustInventoryToZero(cookies, mallid, OutTerminatedProductList, '已终止商品库存调0');

            console.log('已下架/已终止,商品库存调整完成');
        } catch (e) {
            console.log('已下架/已终止,商品库存调整失败！');
            console.error(e);
        }
    }

    // 通用的库存调整函数
    async function adjustInventoryToZero(cookies, mallid, getProductListFunction, functionName) {
        console.log(`开始${functionName}操作------------------------`)
        while (true) {
            try {
                const pageSize = 100
                const page_query = await getProductListFunction(pageSize, cookies, mallid, 1)

                // 检查是否还有数据
                if (!page_query.result || !page_query.result.total || page_query.result.total === 0) {
                    console.log('没有更多数据，操作结束')
                    break
                }

                let pageNum = Math.ceil(page_query.result.total / pageSize)
                console.log(`总数据:${page_query.result.total}, 总页数:${pageNum}`)
                if (pageNum >= 99) {
                    const randomNum = Math.floor(Math.random() * (98 - 50 + 1)) + 50;
                    pageNum = randomNum;
                }
                for (let page = pageNum; page > 0; page--) {
                    await delay(1000)
                    // console.log('当前页数:', page);
                    console.log(`当前页数: ${page}，当前时间: ${new Date().toLocaleTimeString()}`);

                    try {
                        // 获取当前页的数据
                        const currentPageData = await getProductListFunction(pageSize, cookies, mallid, page);

                        // 遍历当前页的 pageItems
                        for (const item of currentPageData.result.pageItems) {
                            try {
                                console.log('-----------------------------------------------------');
                                await delay(500)
                                const productId = item.productId;
                                const productSkcId = item.productSkcId;
                                console.log(`商品ID: ${productId},SKC ID: ${productSkcId}`);
                                let productSkuIdList = [];
                                for (let productSkuSummary of item.productSkuSummaries) {
                                    if (productSkuSummary.virtualStock > 0) {
                                        let productSkuId = productSkuSummary.productSkuId;
                                        productSkuIdList.push(productSkuId);
                                    }
                                }
                                console.log(`productSkuIdList: ${productSkuIdList}`);
                                if (productSkuIdList.length > 0) {
                                    // 获取warehouseId信息
                                    let skuStockChangeList = [];
                                    let warehouseInforesult = await queryBtgProductStockInfo(productId, productSkuIdList, cookies, mallid);
                                    for (let productStock of warehouseInforesult.result.productStockList) {
                                        let productSkuId = productStock.productSkuId;
                                        for (let warehouseStock of productStock.warehouseStockList) {
                                            let warehouseId = warehouseStock.warehouseInfo.warehouseId;
                                            let stockAvailable = warehouseStock.stockAvailable;

                                            let skuStockChange = {
                                                "productSkuId": productSkuId,
                                                "stockDiff": -stockAvailable,
                                                "currentStockAvailable": stockAvailable,
                                                "currentShippingMode": 1,
                                                "warehouseId": warehouseId
                                            };
                                            skuStockChangeList.push(skuStockChange);
                                        }
                                    }
                                    const payload = {
                                        "productId": productId,
                                        "skuStockChangeList": skuStockChangeList,
                                        "skuTypeChangeList": [],
                                        "isCheckVersion": true
                                    };
                                    // console.log('payload:', payload);

                                    const updateResult = await updateMmsBtgProductSalesStock(payload, cookies, mallid);

                                    if (updateResult.success && updateResult.result?.isSuccess) {
                                        console.log('库存更新成功：', updateResult);
                                    } else {
                                        const errorMessage = updateResult.errorMsg || '未知错误，库存更新失败';
                                        console.warn('库存更新失败:', errorMessage, updateResult);
                                    }
                                }

                            } catch (error) {
                                console.error('处理商品库存时出错:', error);
                                continue;
                            }

                        }
                    } catch (e) {
                        console.error('当前页数据异常:', e)
                        continue;
                    }
                }
                // 一轮执行完成后，等待3分钟
                console.log('本轮执行完成,等待3分钟后继续检查...')
                await delay(180000) // 3分钟 = 180000毫秒
                // 清除控制台输出
                console.clear();
            } catch (e) {
                console.error('数据异常！', e)
                console.log('发生错误,等待1分钟后重试...')
                await delay(60000)
            }
        }
    }
    async function newadjustInventoryToZero(cookies, mallid, getProductListFunction, functionName, concurrency = 5) {
        console.log(`开始${functionName}操作------------------------`);

        // 并发控制函数
        async function runWithConcurrencyLimit(tasks, limit) {
            return new Promise((resolve, reject) => {
                let index = 0;
                let completedCount = 0;

                function next() {
                    if (index >= tasks.length) {
                        if (completedCount === tasks.length) resolve();
                        return;
                    }
                    const task = tasks[index++];
                    task()
                        .then(() => {
                            completedCount++;
                            if (completedCount === tasks.length) {
                                resolve();
                            }
                            next();
                        })
                        .catch(reject);
                }

                for (let i = 0; i < Math.min(limit, tasks.length); i++) {
                    next();
                }
            });
        }
        while (true) {
            try {
                const pageSize = 100;
                const page_query = await getProductListFunction(pageSize, cookies, mallid, 1);

                if (!page_query?.result?.total || page_query.result.total === 0) {
                    console.log('没有更多数据，操作结束');
                    return;
                }

                let pageNum = Math.ceil(page_query.result.total / pageSize);
                console.log(`总数据:${page_query.result.total}, 总页数:${pageNum}`);
                if (pageNum >= 99) {
                    pageNum = Math.floor(Math.random() * (98 - 50 + 1)) + 50;
                }

                for (let page = pageNum; page > 0; page--) {
                    await delay(300); // 缩短延迟
                    // console.log('当前页数:', page);
                    console.log(`当前页数: ${page}，当前时间: ${new Date().toLocaleTimeString()}`);

                    try {
                        const currentPageData = await getProductListFunction(pageSize, cookies, mallid, page);

                        // 构建任务列表
                        const itemTasks = currentPageData.result.pageItems.map(item => async () => {
                            try {
                                const productId = item.productId;
                                const productSkcId = item.productSkcId;
                                console.log(`商品ID: ${productId}, SKC ID: ${productSkcId}`);

                                let productSkuIdList = [];
                                for (let summary of item.productSkuSummaries) {
                                    if (summary.virtualStock > 0) {
                                        productSkuIdList.push(summary.productSkuId);
                                    }
                                }

                                if (productSkuIdList.length != 0) {
                                    let skuStockChangeList = [];
                                    let warehouseInfo = await queryBtgProductStockInfo(productId, productSkuIdList, cookies, mallid);

                                    for (let stockItem of warehouseInfo.result.productStockList) {
                                        const productSkuId = stockItem.productSkuId;
                                        for (let whStock of stockItem.warehouseStockList) {
                                            const warehouseId = whStock.warehouseInfo.warehouseId;
                                            const stockAvailable = whStock.stockAvailable;

                                            skuStockChangeList.push({
                                                productSkuId,
                                                stockDiff: -stockAvailable,
                                                currentStockAvailable: stockAvailable,
                                                currentShippingMode: 1,
                                                warehouseId
                                            });
                                        }
                                    }

                                    const payload = {
                                        productId,
                                        skuStockChangeList,
                                        skuTypeChangeList: [],
                                        isCheckVersion: true
                                    };

                                    const updateResult = await updateMmsBtgProductSalesStock(payload, cookies, mallid);
                                    if (updateResult.success && updateResult.result?.isSuccess) {
                                        console.log('库存更新成功：', updateResult);
                                    } else {
                                        const errorMessage = updateResult.errorMsg || '未知错误，库存更新失败';
                                        console.warn('库存更新失败:', errorMessage, updateResult);
                                    }
                                }

                            } catch (error) {
                                console.error('处理单个商品出错:', error);
                            }
                        });

                        // 启动并发处理
                        await runWithConcurrencyLimit(itemTasks, concurrency);
                    } catch (e) {
                        console.error('获取页面数据失败:', e);
                        continue;
                    }
                }

                console.log('本轮执行完成,等待3分钟后继续检查...');
                await delay(180000); // 3分钟

            } catch (e) {
                console.error('数据异常！', e);
                console.log('发生错误,等待1分钟后重试...');
                await delay(60000); // 1分钟
            }
        }
    }
    //获取当前域名
    function getCurrentDomain() {
        const currentUrl = window.location.href;
        // return currentUrl.includes('agentseller.temu.com') ? 'agentseller.temu.com/api/kiana/gamblers' : 'seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/gambit';
        return currentUrl.includes('agentseller.temu.com') ? 'agentseller.temu.com/api/kiana/gamblers' : null;
    }
    function seckillPromotionMatch(activityType, searchScrollContext, cookies, mallid) {
        let payload = {
            'searchScrollContext': searchScrollContext,
            'rowCount': 50,
            'activityType': activityType,
            'addSite': true

        }
        const domain = getCurrentDomain();
        const new_url = `https://${domain}/marketing/enroll/semi/scroll/match`
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: new_url,
                data: JSON.stringify(payload),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const data1 = JSON.parse(response.responseText);
                        resolve(data1); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    //秒杀类、大促活动配置
    async function seckillPromotionMain(activityType, cookies, mallid, sessionSelect, VS_Code) {
        console.log('开始秒杀类/大促活动配置操作------------------------')
        const domain = getCurrentDomain();
        if (domain == null) {
            console.log('请先跳转到活动页面')
            alert('请先跳转到活动页面')
            return
        }
        const match_info = await seckillPromotionMatch(activityType, '', cookies, mallid)
        let searchScrollContext = match_info.result.searchScrollContext
        if (match_info.result.hasMore) {
            let w_result = []
            while (true) {
                try {
                    const new_wkitems = await seckillPromotionMatch(activityType, searchScrollContext, cookies, mallid)
                    const continue_scroll = new_wkitems.result.hasMore
                    searchScrollContext = new_wkitems.result.searchScrollContext
                    if (!continue_scroll) {
                        console.log('翻页完毕')
                        if (w_result.length > 0) {
                            const p_wk_data = await MSProductActivityVerifyPrice(activityType, w_result, cookies, mallid)
                            logSubmissionResults(w_result, p_wk_data);
                            w_result.length = 0;
                        }
                        return
                    }
                    const new_productList = new_wkitems.result.matchList
                    if (Array.isArray(new_productList) && new_productList.length > 0) {
                        // const w_result = []
                        for (let i = 0; i <= new_productList.length; i++) {
                            await delay(500)
                            const w_items = new_productList[i]
                            try {
                                if (!w_items) {
                                    continue
                                }
                                const salesStock = w_items.salesStock
                                const productId = w_items.productId
                                const skcId = w_items.activitySiteInfoList[0].skcList[0].skcId
                                const skuId = w_items.activitySiteInfoList[0].skcList[0].skuList[0].skuId
                                if (salesStock < activeInventoryCheck) {
                                    console.log(`productId: ${productId}, skcId: ${skcId}, skuId: ${skuId},销售库存:${salesStock}, 库存小于50, 跳过`);
                                    continue
                                }
                                const suggestActivityPrice = w_items.activitySiteInfoList[0].skcList[0].skuList[0].suggestActivityPrice
                                const extCode = w_items.activitySiteInfoList[0].skcList[0].skuList[0].extCode
                                const currency = w_items.activitySiteInfoList[0].skcList[0].skuList[0].currency
                                const targetActivityStock = w_items.targetActivityStock
                                const siteId = w_items.sites[0].siteId
                                console.log(productId, skcId, skuId, suggestActivityPrice, extCode, currency, targetActivityStock)
                                if (!productId | !skcId | !skuId | !suggestActivityPrice | !extCode | !currency | !targetActivityStock) {
                                    continue
                                }
                                // 检查extCode,20250320增加泛欧判断，跳过处理
                                let parts = extCode.split('_');
                                let region = parts[2];// 'FO'
                                if (region == 'FO') {
                                    console.log('FO跳过处理')
                                    continue;
                                }

                                const erp_result = await WeekendayVerifyPrice(activityType, skcId, extCode, suggestActivityPrice / 100, currency, VS_Code, mallid)
                                console.log('erp_info:', erp_result)
                                if (erp_result.state == 999) {
                                    console.log('请检查版本，时间状态,请检查账号是否参加活动')
                                    return
                                }
                                if (erp_result.state == 2 && targetActivityStock > 0) {
                                    const session_result = await await MSProductActivitySiteList(productId, activityType, cookies, mallid)
                                    if (session_result.result.list.length > 0) {
                                        // const sessionId = session_result.result.list.find(item => item.sessionStatus === 2)?.sessionId
                                        // 根据选择的场次类型获取sessionIds
                                        let sessionIds;
                                        if (sessionSelect === '临近场次') {
                                            // 按场次时间排序，取最近的一条
                                            const nearestSession = session_result.result.list.sort((a, b) => a.startTime - b.startTime)[0];
                                            sessionIds = nearestSession ? [nearestSession.sessionId] : [];
                                            // console.log('sessionIds:', sessionIds);
                                        } else {
                                            // 获取所有场次的sessionId
                                            sessionIds = session_result.result.list.map(item => item.sessionId);
                                        }
                                        // console.log('sessionIds:', sessionIds);
                                        // 构造productList
                                        const productList = {
                                            "productId": productId,
                                            "activityStock": targetActivityStock,
                                            "siteInfoList": [
                                                {
                                                    "siteId": siteId,
                                                    "skcList": [
                                                        {
                                                            "skcId": skcId,
                                                            "skuList": [
                                                                {
                                                                    "skuId": skuId,
                                                                    "activityPrice": suggestActivityPrice
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    ...(activityType === 1 ? { "activityEnrollPrivilegeType": 0 } : {})
                                                }
                                            ],
                                            "sessionIds": sessionIds
                                        };
                                        w_result.push(productList);
                                    }
                                }
                            } catch (error) {
                                console.log('当前产品处理失败，跳过', error)
                                continue
                            }
                        }
                        if (Array.isArray(w_result) && w_result.length >= 30) {
                            const p_wk_data = await MSProductActivityVerifyPrice(activityType, w_result, cookies, mallid)
                            logSubmissionResults(w_result, p_wk_data);
                            w_result.length = 0;
                        }
                    }
                } catch (error) {
                    console.log('处理当前页报错:', error)
                    continue
                }
            }
        }
    }

    async function fopopupRemoved() {
        try {
            // 选择并移除第一个元素
            // const modalmaskElement = document.querySelector('div[data-testid="beast-core-modal-mask"]');
            // if (modalmaskElement) {
            //     modalmaskElement.remove();
            // }

            // const modalElement = document.querySelector('div[data-testid="beast-core-modal"]');
            // if (modalElement) {
            //     modalElement.remove();
            // }

            // 获取所有匹配的 modal 元素
            const modalElements = document.querySelectorAll('div[data-testid="beast-core-modal"]');

            // 遍历每个 modal 元素
            modalElements.forEach(modalElement => {
                // 检查是否包含特定的子元素 div.modal-copy-goods-publish_container__238YF
                // const targetChild = modalElement.querySelector('div.modal-copy-goods-publish_container__238YF');
                const targetChild = modalElement.querySelector('div[class*="modal-copy-goods-publish_container__"]');
                if (targetChild) {
                    // 移除 modal 元素上面一个 div[data-testid="beast-core-modal-mask"]
                    const maskElement = modalElement.previousElementSibling;
                    if (maskElement && maskElement.matches('div[data-testid="beast-core-modal-mask"]')) {
                        maskElement.remove();
                    }
                    // 如果包含，则移除该 modal 元素
                    modalElement.remove();
                }
            });

        } catch (e) {
            console.error(e);
        }
    }

    function sendUnpublishedProductData(skcId, extCode, price, currency, sitePriceList, versions, accountId, retryCount = 3) {
        return new Promise((resolve, reject) => {
            const makeRequest = (attemptCount) => {
                if (attemptCount <= 0) {
                    reject(new Error('达到最大重试次数'));
                    return;
                }
                GM_xmlhttpRequest({
                    timeout: 60000,
                    method: 'POST',
                    url: 'https://eagletemu.jxkhc02.com/api/TemuProductAudit/CheckProductAuditPrice',
                    data: JSON.stringify({
                        "data": {
                            "skc": skcId,
                            "skuNo": extCode,
                            "platformPrice": price,
                            "currency": currency,
                            "sitePriceList": sitePriceList,
                        },
                        "accountId": accountId,
                        "versions": versions
                    }),
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    onload: function (response) {
                        try {
                            const data1 = JSON.parse(response.responseText);
                            resolve(data1);
                        } catch (e) {
                            console.error("ERP Failed to parse JSON:", e);
                            // 解析错误时重试
                            setTimeout(() => makeRequest(attemptCount - 1), 1000);
                        }
                    },
                    onerror: function (error) {
                        console.error(`ERP Request Error (Attempt ${retryCount - attemptCount + 1}):`, error);
                        // 请求错误时重试
                        setTimeout(() => makeRequest(attemptCount - 1), 1000);
                    },
                    ontimeout: function () {
                        console.error(`ERP Request Timeout (Attempt ${retryCount - attemptCount + 1})`);
                        // 超时时重试
                        setTimeout(() => makeRequest(attemptCount - 1), 1000);
                    }
                });
            };

            makeRequest(retryCount);
        });
    }
    async function syncUnpublishedDataToSite(cookies, mallid, VS_Code) {
        console.log('开始同步未发布站点数据操作------------------------');
        const timestampList = generateMonthlyTimestamps()
        try {
            for (const timeItem of timestampList.reverse()) {
                if (!timeItem) continue;

                const { timeBegin, timeEnd } = timeItem;
                const p_payload = {
                    'timeBegin': timeBegin,
                    'timeEnd': timeEnd,
                    'timeType': 1,
                    'secondarySelectStatusList': [10, 11],
                    'supplierTodoTypeList': [],
                }

                const rspList = await getNewProductLifecycleData(1, 50, p_payload, cookies, mallid)
                const totalItems = rspList?.result?.total;
                if (totalItems <= 0) {
                    console.log('当前时间段无数据');
                    continue;
                }

                let pageNum = Math.min(Math.ceil(totalItems / 50), 99);//最大99页
                console.log(`当前时间段: ${new Date(timeBegin).toLocaleDateString()} - ${new Date(timeEnd).toLocaleDateString()}, 总页数: ${pageNum}`);
                for (let page = 1; page <= pageNum; page++) {
                    try {
                        const response = await getNewProductLifecycleData(page, 50, p_payload, cookies, mallid)
                        console.log('response : ', response)
                        if (!response?.result?.dataList?.length) {
                            console.log(`第 ${page} 页没有数据`);
                            continue;
                        }

                        for (const item of response.result.dataList) {
                            const priceOrderId = item.skcList[0]?.supplierPriceReviewInfoList[0]?.priceOrderId;
                            const skcId = item.skcList[0]?.skcId;
                            const extCode = item.skcList[0]?.skuList[0]?.extCode;
                            const skuId = item.skcList[0]?.skuList[0]?.skuId;
                            const price = item.skcList[0]?.skuList[0]?.siteSupplierPriceList[0]?.supplierPriceValue;
                            const suggestPriceCurrency = item.supplierPriceCurrencyType;
                            console.log(`skcId: ${skcId}, skuId: ${skuId}, extCode: ${extCode}, price: ${price}, suggestPriceCurrency: ${suggestPriceCurrency}`);
                            // 确保所有必要的属性都存在
                            if (!priceOrderId || !skcId || !extCode || !skuId || !price) {
                                console.log('缺少必要的数据，跳过当产品');
                                continue;
                            }
                            await delay(1000); // 等待一秒
                            let extCode_suffix = extCode.includes('_EU_FO')
                            if (!extCode_suffix) {
                                const verify_data = await sendUnpublishedProductData(skcId, extCode, price / 100, suggestPriceCurrency, null, VS_Code, mallid);
                                console.log('erp_info:', verify_data);
                                // 处理验证结果
                                if (!verify_data || verify_data.state == null) {
                                    console.log('未能获取有效的验证信息', verify_data);
                                    continue;
                                }
                                const verify_code = verify_data.state;
                                // 检查插件版本是否过期
                                if (verify_code === 999) {
                                    alert('插件需要在规定时间内使用，或请及时更新版本');
                                    return; // 结束函数的执行
                                }
                            }
                            else {
                                console.log('处理泛欧FO商品:', extCode, skcId);
                                const Fositelist = await queryProductSkuPriceAndStatus_FO(skuId, cookies, mallid);
                                if (Fositelist?.result?.siteSupplierPriceList?.length > 0) {
                                    // 筛选出 priceReviewStatus 等于 2 的数据
                                    const compInfo = Fositelist.result.siteSupplierPriceList.filter(item => item.priceReviewStatus === 2);
                                    if (compInfo.length === 0) {
                                        console.log('没有获取到的站点数据');
                                        continue;
                                    }
                                    // 构建 extractedList
                                    const extractedList = compInfo.map(item => ({
                                        platformPrice: item.supplierPriceValue / 100,
                                        currency: item.supplierPriceCurrencyType,
                                        site: getNameToCode(item.siteName)
                                    }));
                                    // 发送验证价格请求
                                    const verify_data = await sendUnpublishedProductData(skcId, extCode, null, null, extractedList, VS_Code, mallid);
                                    console.log('erp_info:', verify_data);
                                    const verify_code = verify_data.state;
                                    // 检查插件版本是否过期
                                    if (verify_code === 999) {
                                        alert('插件需要在规定时间内使用，或请及时更新版本');
                                        return; // 结束函数的执行
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`第 ${page} 页处理失败:`, error);
                        continue;
                    }
                }
            }
        } catch (error) {
            console.error('同步过程中发生错误: ', error);
        }

    }

    async function getNewProductLifecycleData(pageNum, pageSize, additionalParams = {}, cookies, mallid) {
        const retryCount = 3
        return new Promise((resolve, reject) => {
            const makeRequest = (attemptCount) => {
                if (attemptCount <= 0) {
                    reject(new Error('达到最大重试次数'));
                    return;
                }
                // 基础请求参数
                const baseParams = {
                    'pageNum': pageNum,
                    'pageSize': pageSize,
                };

                // 合并基础参数和额外参数
                const requestParams = { ...baseParams, ...additionalParams };
                console.log('requestParams:', requestParams);

                GM_xmlhttpRequest({
                    timeout: 60000, // 设置超时为 5000 毫秒（5 秒）
                    method: 'POST',
                    url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier',
                    data: JSON.stringify(
                        requestParams
                    ),
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Cookie': cookies,
                        'mallid': mallid,
                    },
                    onload: function (response) {
                        try {
                            const result = JSON.parse(response.responseText);
                            resolve(result);
                        } catch (e) {
                            console.error("Failed to parse JSON:", e);
                            // 解析错误时重试
                            setTimeout(() => makeRequest(attemptCount - 1), 1000);
                        }
                    },
                    onerror: function (error) {
                        console.error(`Request Error (Attempt ${retryCount - attemptCount + 1}):`, error);
                        // 请求错误时重试
                        setTimeout(() => makeRequest(attemptCount - 1), 1000);
                    },
                    ontimeout: function () {
                        console.error(`Request Timeout (Attempt ${retryCount - attemptCount + 1})`);
                        // 超时时重试
                        setTimeout(() => makeRequest(attemptCount - 1), 1000);
                    }

                });
            };

            makeRequest(retryCount);
        });
    }
    async function getNewProductLifecycleData_xhr(pageNum, pageSize, additionalParams = {}, cookies, mallid) {
        const retryCount = 3;
        return new Promise((resolve, reject) => {
            const makeRequest = (attemptCount) => {
                if (attemptCount <= 0) {
                    reject(new Error('达到最大重试次数'));
                    return;
                }

                // 基础请求参数
                const baseParams = {
                    'pageNum': pageNum,
                    'pageSize': pageSize
                };

                // 合并基础参数和额外参数
                const requestParams = { ...baseParams, ...additionalParams };

                const xhr = new XMLHttpRequest();
                xhr.timeout = 60000; // 设置超时为 60 秒
                xhr.open('POST', 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier', true);

                // 设置请求头
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('mallid', mallid);

                xhr.onload = function () {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const result = JSON.parse(xhr.responseText);
                            resolve(result);
                        } catch (e) {
                            console.error("解析 JSON 失败:", e);
                            // 解析错误时重试
                            setTimeout(() => makeRequest(attemptCount - 1), 1000);
                        }
                    } else {
                        console.error(`HTTP 错误（尝试 ${retryCount - attemptCount + 1}): ${xhr.status}`);
                        // HTTP 错误时重试
                        setTimeout(() => makeRequest(attemptCount - 1), 1000);
                    }
                };

                xhr.onerror = function () {
                    console.error(`请求错误（尝试 ${retryCount - attemptCount + 1})`);
                    // 请求错误时重试
                    setTimeout(() => makeRequest(attemptCount - 1), 1000);
                };

                xhr.ontimeout = function () {
                    console.error(`请求超时（尝试 ${retryCount - attemptCount + 1})`);
                    // 超时时重试
                    setTimeout(() => makeRequest(attemptCount - 1), 1000);
                };

                try {
                    xhr.send(JSON.stringify(requestParams));
                } catch (e) {
                    console.error(`发送请求失败（尝试 ${retryCount - attemptCount + 1}):`, e);
                    // 发送失败时重试
                    setTimeout(() => makeRequest(attemptCount - 1), 1000);
                }
            };

            makeRequest(retryCount);
        });
    }
    function submitTrafficLimitProductPriceAdjustment(payload, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                timeout: 5000, // 设置超时为 5000 毫秒（5 秒）
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/magnus/price/priceAdjust/gmpProductBatchAdjustPrice',
                data: JSON.stringify(
                    payload
                ),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        resolve(result); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                },
                ontimeout: function () {
                    reject('请求超时');
                }
            });
        });
    }
    function queryProductSkuPriceAndStatus_FO(productSkuId, cookies, mallid) {
        return new Promise((resolve, reject) => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://seller.kuajingmaihuo.com/marvel-mms/cn/api/kiana/xmen/select/queryProductSkuPriceAndStatus',
                data: JSON.stringify({ 'productSkuId': productSkuId }),
                headers: {
                    'Content-Type': 'application/json',
                    "Cookie": cookies,
                    'mallid': mallid,
                },
                onload: function (response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        resolve(result); // 解析成功，返回价格
                    } catch (e) {
                        console.error("Failed to parse JSON:", e);
                        reject(e); // 解析失败，拒绝 Promise
                    }
                },
                onerror: function (error) {
                    console.error("Request Error:", error);
                    reject(error); // 请求失败，拒绝 Promise
                }
            });
        });
    }
    async function adjustProductPriceDuringTrafficLimit(cookies, mallid, VS_Code) {
        console.log('开始流量限制中产品调价操作------------------------');
        // alert('暂未开放！！！')
        // return;

        const additionalParams = {
            'supplierTodoTypeList': [13],
            // 'productSkcIdList': [47785443161]
        };
        // 获取总数据
        const producNum = await getNewProductLifecycleData(1, 50, additionalParams, cookies, mallid);
        if (!producNum?.result?.total) {
            console.log('获取数据失败');
            return;
        }

        console.log('总数据:', producNum.result.total);
        const pageNum = Math.min(Math.ceil(producNum.result.total / 50), 99);
        for (let page = pageNum; page > 0; page--) {
            try {
                console.log('当前页数:', page)
                const productList = await getNewProductLifecycleData(page, 50, additionalParams, cookies, mallid);
                // 验证数据有效性
                if (!isValidData(productList)) continue;
                for (const item of productList.result.dataList) {
                    try {
                        const skcId = item.skcList[0]?.skcId;
                        const extCode = item.skcList[0]?.skuList[0]?.extCode;
                        const skuId = item.skcList[0]?.skuList[0]?.skuId;
                        const targetSupplyPrice = item.skcList[0]?.skuList[0]?.siteSupplierPriceList[0]?.targetSupplyPrice;
                        const suggestPriceCurrency = item.supplierPriceCurrencyType;
                        const productId = item.productId;
                        const supplierId = item.supplierId;
                        const productName = item.productName;
                        console.log(`skcId: ${skcId}, skuId: ${skuId}, extCode: ${extCode}, targetSupplyPrice: ${targetSupplyPrice}, suggestPriceCurrency: ${suggestPriceCurrency}`);
                        // 确保必要的属性都存在
                        if (!skcId || !extCode || !skuId || !targetSupplyPrice) {
                            console.log('缺少必要的数据，跳过当前循环');
                            continue;
                        }
                        await delay(500);
                        let extCode_suffix = extCode.includes('_EU_FO')
                        let sitePriceList = [];
                        if (!extCode_suffix) {
                            const platform_verify_data = await PlatformsendRequestReducePrice(skcId, extCode, targetSupplyPrice / 100, suggestPriceCurrency, sitePriceList, VS_Code, mallid);
                            console.log(platform_verify_data)
                            const status_platfom = platform_verify_data.state

                            if (status_platfom === 999) {
                                alert('插件需要在规定时间内使用，或请及时更新版本');
                                return; // 结束函数的执行
                            }
                            if (status_platfom == 2) {
                                const oldSupplyPrice = item.skcList[0]?.skuList[0]?.siteSupplierPriceList[0]?.supplierPriceValue;
                                const siteId = item.semiHostedBindSiteIds[0];

                                const payload = {
                                    'adjustItems': [
                                        {
                                            'productName': productName,
                                            'productSkcId': skcId,
                                            'skuAdjustList': [
                                                {
                                                    'targetPriceCurrency': suggestPriceCurrency,
                                                    'oldPriceCurrency': suggestPriceCurrency,
                                                    'oldSupplyPrice': oldSupplyPrice,
                                                    'skuId': skuId,
                                                    'targetSupplyPrice': targetSupplyPrice,
                                                    'siteId': siteId
                                                }
                                            ],
                                            'productId': productId,
                                            'supplierId': supplierId
                                        }
                                    ],
                                    'isFromSearchLimitAdjust': true
                                }
                                console.log('payload', payload)
                                const accept_status = await submitTrafficLimitProductPriceAdjustment(payload, cookies, mallid)
                                console.log('接受调价', accept_status)
                            }
                        }
                        else {
                            try {
                                console.log('处理泛欧FO商品:', extCode, skcId);
                                const dataListFo = await queryProductSkuPriceAndStatus_FO(skuId, cookies, mallid);
                                if (dataListFo?.result?.siteSupplierPriceList?.length > 0) {
                                    // 筛选出 searchLimitStatus 等于 1 的数据
                                    const compInfo = dataListFo.result.siteSupplierPriceList.filter(item => item.searchLimitStatus === 1);

                                    if (compInfo.length === 0) {
                                        console.log('没有需要调价的站点数据');
                                        continue;
                                    }

                                    console.log('满足 searchLimitStatus=1 的数据:', compInfo);

                                    // 构建 extractedList
                                    const extractedList = compInfo.map(item => ({
                                        platformPrice: item.targetSupplyPrice / 100, // 转为正常价格格式
                                        currency: item.supplierPriceCurrencyType,
                                        site: getNameToCode(item.siteName) // 获取站点代码
                                    }));
                                    console.log('extractedList', extractedList);

                                    // 发送验证价格请求
                                    const verify_data = await PlatformsendRequestReducePrice(skcId, extCode, null, null, extractedList, VS_Code, mallid);
                                    console.log('erp_info:', verify_data);

                                    const verify_code = verify_data.state;
                                    if (verify_code === 999) {
                                        alert('插件需要在规定时间内使用，或请及时更新版本');
                                        return; // 结束函数的执行
                                    }

                                    if (verify_code === 2) {
                                        const siteAcceptList = verify_data.siteAcceptList;
                                        // 筛选出 siteAcceptList 中 state=2 的站点
                                        const acceptedSites = siteAcceptList.filter(site => site.state === 2).map(site => site.site);
                                        if (acceptedSites.length === 0) {
                                            console.log('没有通过验证的站点');
                                            continue;
                                        }

                                        // 构建调价请求参数
                                        const payload = {
                                            'adjustItems': [{
                                                'productName': productName,
                                                'productSkcId': skcId,
                                                'skuAdjustList': compInfo
                                                    .filter(item => acceptedSites.includes(getNameToCode(item.siteName)))
                                                    .map(item => ({
                                                        'targetPriceCurrency': item.supplierPriceCurrencyType,
                                                        'oldPriceCurrency': item.supplierPriceCurrencyType,
                                                        'oldSupplyPrice': item.supplierPriceValue,
                                                        'skuId': skuId,
                                                        'targetSupplyPrice': item.targetSupplyPrice,
                                                        'siteId': item.siteId
                                                    })),
                                                'productId': productId,
                                                'supplierId': supplierId
                                            }],
                                            'isFromSearchLimitAdjust': true
                                        };

                                        console.log("调价请求参数:", payload);


                                        const accept_status = await submitTrafficLimitProductPriceAdjustment(payload, cookies, mallid);
                                        console.log('泛欧商品调价结果:', accept_status);
                                    }

                                }
                            } catch (error) {
                                console.error('处理泛欧商品时出错:', error);
                                continue;
                            }
                        }
                    } catch (error) {
                        console.log('处理单个商品时出错:', error)
                    }
                }
            } catch (error) {
                console.log('获取当前页面数据失败', error)
                continue
            }
        }
    }

}
)();
